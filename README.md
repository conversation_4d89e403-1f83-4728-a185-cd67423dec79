# 发票申请查询系统

一个基于 PyWebView 的桌面应用程序，用于查询和展示天猫供应商发票申请数据。

## 功能特性

- 🚀 自动登录功能 - 一键登录并自动获取Cookie
- 🔐 Cookie 配置管理
- 📤 简化发票上传流程
- 📁 自动打开PDF上传对话框
- 📋 发票申请数据查询
- 📊 数据可视化展示
- 📄 分页浏览
- 🔄 实时刷新
- 💰 金额格式化显示
- ⏰ 倒计时显示

## 安装要求

- Python 3.7+
- Windows/macOS/Linux

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行应用

```bash
python run.py
```

或者直接运行：

```bash
python main.py
```

## 使用说明

### 1. 获取 Cookie

#### 方式一：自动登录（推荐）
1. 启动应用后，点击绿色的"自动登录"按钮
2. 系统会自动打开浏览器并跳转到登录页面
3. 在浏览器中完成登录操作
4. 系统会自动检测登录状态并获取Cookie
5. 登录成功后Cookie会自动保存

#### 方式二：手动获取
1. 手动打开浏览器，登录天猫供应商后台
2. 进入发票申请页面
3. 按 F12 打开开发者工具
4. 切换到 Network 标签页
5. 刷新页面或执行查询操作
6. 找到 `pageQueryInvoiceApply` 请求
7. 复制请求头中的完整 Cookie 字符串

### 2. 配置应用

1. 启动应用后，在 Cookie 配置区域粘贴复制的 Cookie
2. 点击"设置Cookie"按钮
3. 等待成功提示

### 3. 上传发票

1. 点击"上传发票"按钮
2. 系统会自动完成所有操作：
   - 打开发票申请页面
   - 点击发票录入按钮
   - 打开录入弹窗
   - 点击上传PDF按钮
   - 打开文件选择对话框
3. 在文件对话框中选择PDF文件即可完成上传

### 4. 查询数据

1. 点击"查询数据"按钮
2. 等待数据加载完成
3. 使用分页功能浏览更多数据
4. 点击"刷新"按钮获取最新数据

## 数据字段说明

- **订单号**: 天猫订单编号
- **供应商**: 供应商名称
- **发票抬头**: 开票公司名称
- **发票金额**: 开票金额（元）
- **申请时间**: 发票申请提交时间
- **截止时间**: 开票截止时间
- **倒计时**: 距离截止时间的剩余时间
- **纳税人识别号**: 开票公司税号

## 技术架构

- **前端**: HTML5 + CSS3 + JavaScript
- **后端**: Python + PyWebView
- **网络请求**: Requests 库
- **界面框架**: PyWebView (基于系统 WebView)

## 文件结构

```
├── main.py           # 主程序文件
├── index.html        # 前端界面
├── run.py           # 启动脚本
├── requirements.txt  # 依赖列表
└── README.md        # 说明文档
```

## 注意事项

1. **Cookie 有效期**: Cookie 有时效性，如果查询失败请重新获取
2. **网络连接**: 需要稳定的网络连接访问天猫接口
3. **数据安全**: 请妥善保管 Cookie 信息，不要泄露给他人
4. **使用频率**: 避免频繁请求，以免触发反爬虫机制

## 故障排除

### 1. 依赖安装失败

```bash
# 升级 pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 2. 应用启动失败

- 检查 Python 版本是否为 3.7+
- 确保所有文件都在同一目录下
- 检查是否有杀毒软件拦截

### 3. 数据查询失败

- 检查 Cookie 是否正确设置
- 确认网络连接正常
- 重新获取最新的 Cookie

## 开发说明

如需修改或扩展功能，主要文件说明：

- `main.py`: 后端 API 逻辑，包含数据请求和处理
- `index.html`: 前端界面和交互逻辑
- `run.py`: 启动脚本，包含依赖检查

## 许可证

本项目仅供学习和研究使用，请遵守相关网站的使用条款。
