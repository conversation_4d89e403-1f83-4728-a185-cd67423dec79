# Cookie保存机制说明

## 🎯 问题回答

### 1. 更新Cookie文本框内容后是否同时更新config.json文件？

**✅ 是的，会立即更新config.json文件**

当用户在前端Cookie文本框中输入新Cookie时：
1. 前端调用 `pywebview.api.set_cookie(cookie)`
2. 后端 `set_cookie()` 方法被调用
3. 内部调用 `save_cookie_to_config()` 方法
4. **立即保存到config.json文件的 `saved_cookie` 字段**

### 2. 软件关闭时是否保存config.json文件？

**✅ 不需要，因为已经实时保存**

系统采用**实时保存**机制：
- 用户输入Cookie时：立即保存
- 验证码处理完成时：立即保存
- 规格映射更新时：立即保存
- **软件关闭时不需要额外保存操作**

## 🔄 Cookie保存的两种方式

### 方式1: 用户手动输入Cookie

```javascript
// 前端调用
await pywebview.api.set_cookie(cookieValue);
```

```python
# 后端处理
def set_cookie(self, cookie):
    """设置Cookie并保存到配置文件"""
    cleaned_cookie = self.clean_cookie_string(cookie)
    self.cookie = cleaned_cookie
    # 立即保存到配置文件
    self.save_cookie_to_config(cleaned_cookie)
    return {"status": "success", "message": "Cookie设置成功"}
```

### 方式2: 验证码处理自动更新Cookie

```python
# 验证码处理完成后
def save_new_cookie(self, new_cookie):
    """保存新Cookie到配置文件"""
    # 使用统一的保存方法
    self.save_cookie_to_config(new_cookie)
    print("新Cookie已保存到配置文件")
    return True
```

## 📁 配置文件结构

### config.json文件内容示例

```json
{
  "spec_mappings": [
    {
      "productName": "筷",
      "specModel": "件"
    },
    {
      "productName": "保鲜袋",
      "specModel": "卷"
    }
  ],
  "saved_cookie": "cna=xxx; t=yyy; XSRF-TOKEN=zzz; session=aaa"
}
```

### 字段说明

- **`saved_cookie`**: 主要Cookie存储字段
  - 用户手动输入的Cookie
  - 验证码处理后的新Cookie
  - 程序启动时加载的Cookie

- **`spec_mappings`**: 规格映射配置
  - 产品名称和规格型号的映射关系
  - 添加/删除时立即保存

## 🔧 统一保存机制

### 核心保存方法

```python
def save_cookie_to_config(self, cookie):
    """保存Cookie到配置文件"""
    try:
        config_file = get_config_path()
        config = {}
        
        # 如果配置文件存在，先读取现有配置
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
        
        # 清理并更新Cookie
        cleaned_cookie = self.clean_cookie_string(cookie)
        config['saved_cookie'] = cleaned_cookie
        
        # 立即保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=True, indent=2)
        
        print("Cookie已保存到配置文件")
    except Exception as e:
        print(f"保存Cookie失败: {str(e)}")
```

### 加载机制

```python
def load_saved_cookie(self):
    """从配置文件加载保存的Cookie"""
    try:
        config_file = get_config_path()
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                saved_cookie = config.get('saved_cookie', '')
                if saved_cookie:
                    cleaned_cookie = self.clean_cookie_string(saved_cookie)
                    if cleaned_cookie:
                        self.cookie = cleaned_cookie
                        print("已自动加载保存的Cookie")
    except Exception as e:
        print(f"加载Cookie失败: {str(e)}")
```

## 🔄 完整工作流程

### 程序启动时

1. **加载配置**: 调用 `load_saved_cookie()`
2. **读取文件**: 从config.json读取 `saved_cookie` 字段
3. **设置Cookie**: 如果存在有效Cookie，设置为当前Cookie
4. **前端显示**: Cookie文本框显示当前Cookie

### 用户输入Cookie时

1. **前端输入**: 用户在Cookie文本框输入新Cookie
2. **API调用**: 前端调用 `set_cookie()` API
3. **清理验证**: 后端清理和验证Cookie格式
4. **立即保存**: 调用 `save_cookie_to_config()` 立即保存
5. **更新内存**: 更新当前实例的Cookie变量

### 验证码处理时

1. **检测挑战**: API返回"被挤爆啦"错误
2. **浏览器验证**: 自动打开浏览器进行人机验证
3. **获取新Cookie**: 验证成功后获取新的tmall.com Cookie
4. **立即保存**: 调用 `save_new_cookie()` → `save_cookie_to_config()`
5. **前端更新**: 自动更新前端Cookie文本框
6. **重试请求**: 使用新Cookie重试API请求

## ✅ 保存时机总结

| 操作 | 保存时机 | 保存方法 | 字段名 |
|------|----------|----------|--------|
| 用户输入Cookie | 立即保存 | `save_cookie_to_config()` | `saved_cookie` |
| 验证码处理完成 | 立即保存 | `save_new_cookie()` → `save_cookie_to_config()` | `saved_cookie` |
| 规格映射更新 | 立即保存 | 直接写入config.json | `spec_mappings` |
| 软件关闭 | 不需要 | 已实时保存 | - |

## 🛡️ 数据安全保障

### 实时保存优势

- **数据不丢失**: 每次更改立即保存，避免意外关闭导致数据丢失
- **一致性保证**: 内存中的数据与配置文件始终保持一致
- **用户体验**: 用户操作立即生效，无需等待保存

### 错误处理

- **文件读写异常**: 捕获并记录错误，不影响程序运行
- **Cookie格式错误**: 自动清理和验证Cookie格式
- **配置文件损坏**: 自动创建新的配置文件

### 编码处理

- **UTF-8编码**: 确保中文字符正确保存和读取
- **JSON格式**: 使用标准JSON格式，便于解析和维护
- **字符清理**: 自动清理可能导致编码问题的特殊字符

## 💡 最佳实践

1. **实时保存**: 所有用户操作立即保存到配置文件
2. **统一字段**: 使用 `saved_cookie` 作为唯一Cookie存储字段
3. **错误处理**: 完善的异常捕获和错误提示
4. **数据验证**: 保存前验证和清理数据格式
5. **用户反馈**: 适当的成功/失败提示信息

通过这种机制，确保了用户的Cookie和配置数据始终得到可靠保存，无需担心数据丢失问题！
