<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>批量上传发票</title>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .task-item {
            transition: all 0.3s ease;
        }

        /* 任务状态样式 */
        .task-pending {
            @apply border-gray-200;
        }
        .task-pending .status-icon-wrapper { @apply bg-gray-100; }
        .task-pending .task-status-badge { @apply bg-gray-100 text-gray-700; }

        .task-processing {
            @apply border-blue-200 bg-blue-50;
        }
        .task-processing .status-icon-wrapper { @apply bg-blue-100; }
        .task-processing .task-status-badge { @apply bg-blue-100 text-blue-700; }

        .task-success {
            @apply border-green-200 bg-green-50;
        }
        .task-success .status-icon-wrapper { @apply bg-green-100; }
        .task-success .task-status-badge { @apply bg-green-100 text-green-700; }

        .task-error {
            @apply border-red-200 bg-red-50;
        }
        .task-error .status-icon-wrapper { @apply bg-red-100; }
        .task-error .task-status-badge { @apply bg-red-100 text-red-700; }

        .task-skipped {
            @apply border-yellow-200 bg-yellow-50;
        }
        .task-skipped .status-icon-wrapper { @apply bg-yellow-100; }
        .task-skipped .task-status-badge { @apply bg-yellow-100 text-yellow-700; }

        .progress-bar {
            transition: width 0.3s ease;
        }

        .status-icon {
            transition: all 0.3s ease;
        }

        /* 标签页样式 */
        .tab-button.active {
            @apply border-blue-500 text-blue-600 bg-blue-50;
        }

        .tab-content {
            transition: opacity 0.2s ease;
        }

        .tab-content.hidden {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 任务项动画 */
        .task-item {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .task-completed-animation {
            animation: slideOut 0.3s ease-in forwards;
        }

        @keyframes slideOut {
            to {
                opacity: 0;
                transform: translateX(100px);
            }
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 导航栏 -->
        <div class="mb-4 sm:mb-6">
            <div class="inline-flex p-1 bg-gray-100 rounded-full w-full sm:w-auto">
                <button onclick="window.location.href='index.html'" class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full text-gray-600 hover:text-gray-800">发票管理</button>
                <button class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full bg-white shadow text-gray-800">批量上传</button>
                <button onclick="window.location.href='index.html#spec'" class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full text-gray-600 hover:text-gray-800">规格设置</button>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-2">
                <i class="ri-upload-cloud-line mr-2"></i>批量上传发票
            </h1>
            <p class="text-gray-600">自动化批量上传发票到天猫供应商后台</p>
        </div>

        <!-- 控制面板 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <!-- 文件夹选择 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择发票文件夹</label>
                    <button id="selectFolder" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="ri-folder-line mr-2"></i>选择文件夹
                    </button>
                    <div id="folderPath" class="mt-2 text-sm text-gray-600"></div>
                </div>

                <!-- 操作按钮 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">操作控制</label>
                    <div class="flex space-x-2">
                        <button id="startUpload" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-400" disabled>
                            <i class="ri-play-line mr-1"></i>开始上传
                        </button>
                        <button id="pauseUpload" class="flex-1 bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors disabled:bg-gray-400" disabled>
                            <i class="ri-pause-line mr-1"></i>暂停
                        </button>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">任务统计</label>
                    <div class="bg-gray-50 p-3 rounded-lg">
                        <div class="flex justify-between text-sm">
                            <span>总计: <span id="totalCount">0</span></span>
                            <span>成功: <span id="successCount" class="text-green-600">0</span></span>
                        </div>
                        <div class="flex justify-between text-sm mt-1">
                            <span>失败: <span id="errorCount" class="text-red-600">0</span></span>
                            <span>跳过: <span id="skipCount" class="text-yellow-600">0</span></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进度条 -->
            <div class="mb-4">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                    <span>上传进度</span>
                    <span id="progressText">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progressBar" class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
            </div>

            <!-- 当前状态 -->
            <div id="currentStatus" class="text-sm text-gray-600">
                <i class="ri-information-line mr-1"></i>请选择包含发票PDF文件的文件夹开始
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="border-b border-gray-200">
                <!-- 标签页导航 -->
                <div class="flex">
                    <button id="allTasksTab" class="tab-button active flex-1 px-6 py-4 text-sm font-medium text-center border-b-2 border-blue-500 text-blue-600 bg-blue-50">
                        <i class="ri-list-check-2 mr-2"></i>全部任务
                        <span id="allTasksCount" class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">0</span>
                    </button>
                    <button id="completedTasksTab" class="tab-button flex-1 px-6 py-4 text-sm font-medium text-center border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        <i class="ri-check-double-line mr-2"></i>已完成
                        <span id="completedTasksCount" class="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                    </button>
                </div>
            </div>

            <!-- 任务内容区域 -->
            <div class="relative">
                <!-- 全部任务 -->
                <div id="allTasksContent" class="tab-content active">
                    <div id="allTasksList" class="max-h-96 overflow-y-auto">
                        <div class="p-8 text-center text-gray-500">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>暂无任务，请先选择文件夹</p>
                        </div>
                    </div>
                </div>

                <!-- 已完成任务 -->
                <div id="completedTasksContent" class="tab-content hidden">
                    <div id="completedTasksList" class="max-h-96 overflow-y-auto">
                        <div class="p-8 text-center text-gray-500">
                            <i class="ri-check-double-line text-4xl mb-2"></i>
                            <p>暂无已完成的任务</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务项模板 -->
    <template id="taskItemTemplate">
        <div class="task-item bg-white border border-gray-200 rounded-lg p-4 mb-3 shadow-sm hover:shadow-md transition-all duration-200">
            <div class="flex items-start justify-between">
                <div class="flex items-start space-x-3 flex-1">
                    <!-- 状态图标 -->
                    <div class="flex-shrink-0 mt-1">
                        <div class="status-icon-wrapper w-8 h-8 rounded-full flex items-center justify-center">
                            <i class="status-icon ri-time-line text-gray-400"></i>
                        </div>
                    </div>

                    <!-- 任务信息 -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center mb-2">
                            <h3 class="task-filename text-sm font-semibold text-gray-900 truncate"></h3>
                        </div>

                        <div class="space-y-1">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="ri-building-line mr-1 text-gray-400"></i>
                                <span class="task-company truncate"></span>
                            </div>

                            <div class="flex items-center text-sm text-gray-500">
                                <i class="ri-file-text-line mr-1 text-gray-400"></i>
                                <span>发票号: </span>
                                <span class="task-invoice-no font-mono"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态信息 -->
                <div class="flex-shrink-0 text-right ml-4">
                    <div class="task-status-badge px-2 py-1 rounded-full text-xs font-medium mb-1">
                        <span class="task-status">等待中</span>
                    </div>
                    <div class="task-message text-xs text-gray-500 max-w-32 truncate"></div>
                    <div class="task-timestamp text-xs text-gray-400 mt-1"></div>
                </div>
            </div>
        </div>
    </template>

    <script>
        class InvoiceUploader {
            constructor() {
                this.tasks = [];
                this.completedTasks = [];
                this.currentTaskIndex = 0;
                this.isRunning = false;
                this.isPaused = false;
                this.folderPath = '';
                this.activeTab = 'all';

                this.initElements();
                this.bindEvents();
            }

            initElements() {
                this.selectFolderBtn = document.getElementById('selectFolder');
                this.startUploadBtn = document.getElementById('startUpload');
                this.pauseUploadBtn = document.getElementById('pauseUpload');
                this.folderPathDiv = document.getElementById('folderPath');

                // 标签页元素
                this.allTasksTab = document.getElementById('allTasksTab');
                this.completedTasksTab = document.getElementById('completedTasksTab');
                this.allTasksContent = document.getElementById('allTasksContent');
                this.completedTasksContent = document.getElementById('completedTasksContent');
                this.allTasksList = document.getElementById('allTasksList');
                this.completedTasksList = document.getElementById('completedTasksList');
                this.allTasksCount = document.getElementById('allTasksCount');
                this.completedTasksCount = document.getElementById('completedTasksCount');

                this.currentStatus = document.getElementById('currentStatus');
                this.progressBar = document.getElementById('progressBar');
                this.progressText = document.getElementById('progressText');
                this.totalCount = document.getElementById('totalCount');
                this.successCount = document.getElementById('successCount');
                this.errorCount = document.getElementById('errorCount');
                this.skipCount = document.getElementById('skipCount');
                this.taskTemplate = document.getElementById('taskItemTemplate');
            }

            bindEvents() {
                this.selectFolderBtn.addEventListener('click', () => this.selectFolder());
                this.startUploadBtn.addEventListener('click', () => this.startUpload());
                this.pauseUploadBtn.addEventListener('click', () => this.pauseUpload());

                // 标签页事件
                this.allTasksTab.addEventListener('click', () => this.switchTab('all'));
                this.completedTasksTab.addEventListener('click', () => this.switchTab('completed'));
            }

            switchTab(tabName) {
                this.activeTab = tabName;

                // 更新标签页样式
                if (tabName === 'all') {
                    this.allTasksTab.classList.add('active');
                    this.allTasksTab.classList.remove('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                    this.allTasksTab.classList.add('border-blue-500', 'text-blue-600', 'bg-blue-50');

                    this.completedTasksTab.classList.remove('active', 'border-blue-500', 'text-blue-600', 'bg-blue-50');
                    this.completedTasksTab.classList.add('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'border-transparent');

                    this.allTasksContent.classList.remove('hidden');
                    this.allTasksContent.classList.add('active');
                    this.completedTasksContent.classList.add('hidden');
                    this.completedTasksContent.classList.remove('active');
                } else {
                    this.completedTasksTab.classList.add('active');
                    this.completedTasksTab.classList.remove('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                    this.completedTasksTab.classList.add('border-blue-500', 'text-blue-600', 'bg-blue-50');

                    this.allTasksTab.classList.remove('active', 'border-blue-500', 'text-blue-600', 'bg-blue-50');
                    this.allTasksTab.classList.add('text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300', 'border-transparent');

                    this.completedTasksContent.classList.remove('hidden');
                    this.completedTasksContent.classList.add('active');
                    this.allTasksContent.classList.add('hidden');
                    this.allTasksContent.classList.remove('active');
                }
            }

            async selectFolder() {
                try {
                    this.updateStatus('正在选择文件夹...');
                    const result = await pywebview.api.select_invoice_folder();
                    
                    if (result.status === 'success') {
                        this.folderPath = result.folder_path;
                        this.folderPathDiv.textContent = `已选择: ${this.folderPath}`;
                        this.tasks = result.pdf_files;
                        this.renderTaskList();
                        this.updateStatistics();
                        this.startUploadBtn.disabled = false;
                        this.updateStatus(`已扫描到 ${this.tasks.length} 个PDF文件`);
                    } else {
                        this.updateStatus(result.message, 'error');
                    }
                } catch (error) {
                    this.updateStatus('选择文件夹失败: ' + error.message, 'error');
                }
            }

            renderTaskList() {
                // 渲染全部任务
                this.allTasksList.innerHTML = '';
                if (this.tasks.length === 0) {
                    this.allTasksList.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            <i class="ri-inbox-line text-4xl mb-2"></i>
                            <p>暂无任务，请先选择文件夹</p>
                        </div>
                    `;
                } else {
                    this.tasks.forEach((task, index) => {
                        const taskElement = this.createTaskElement(task, index, 'all');
                        this.allTasksList.appendChild(taskElement);
                    });
                }

                // 渲染已完成任务
                this.completedTasksList.innerHTML = '';
                if (this.completedTasks.length === 0) {
                    this.completedTasksList.innerHTML = `
                        <div class="p-8 text-center text-gray-500">
                            <i class="ri-check-double-line text-4xl mb-2"></i>
                            <p>暂无已完成的任务</p>
                        </div>
                    `;
                } else {
                    this.completedTasks.forEach((task, index) => {
                        const taskElement = this.createTaskElement(task, index, 'completed');
                        this.completedTasksList.appendChild(taskElement);
                    });
                }

                // 更新计数
                this.updateTabCounts();
            }

            createTaskElement(task, index, listType) {
                const template = this.taskTemplate.content.cloneNode(true);
                const taskDiv = template.querySelector('.task-item');

                taskDiv.dataset.index = index;
                taskDiv.dataset.listType = listType;
                taskDiv.classList.add('task-pending');

                // 设置任务信息
                template.querySelector('.task-filename').textContent = task.filename;
                template.querySelector('.task-company').textContent = task.company_name;
                template.querySelector('.task-invoice-no').textContent = task.invoice_number;

                // 设置时间戳
                const timestamp = template.querySelector('.task-timestamp');
                if (task.completedAt) {
                    timestamp.textContent = new Date(task.completedAt).toLocaleTimeString();
                } else {
                    timestamp.textContent = '';
                }

                // 如果是已完成的任务，设置对应的状态
                if (listType === 'completed' && task.status) {
                    this.setTaskElementStatus(taskDiv, task.status, task.message || '');
                }

                return template;
            }

            updateTabCounts() {
                this.allTasksCount.textContent = this.tasks.length;
                this.completedTasksCount.textContent = this.completedTasks.length;
            }

            async startUpload() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.isPaused = false;
                this.currentTaskIndex = 0;
                
                this.startUploadBtn.disabled = true;
                this.pauseUploadBtn.disabled = false;
                this.selectFolderBtn.disabled = true;
                
                this.updateStatus('正在初始化浏览器...');
                
                try {
                    // 初始化浏览器
                    const initResult = await pywebview.api.init_upload_browser();
                    if (initResult.status !== 'success') {
                        throw new Error(initResult.message);
                    }
                    
                    this.updateStatus('浏览器初始化成功，开始处理任务...');
                    
                    // 开始处理任务
                    await this.processAllTasks();
                    
                } catch (error) {
                    this.updateStatus('上传失败: ' + error.message, 'error');
                } finally {
                    this.finishUpload();
                }
            }

            async processAllTasks() {
                try {
                    this.updateStatus('开始以订单为主导的上传流程...');

                    // 调用新的订单主导模式
                    const result = await pywebview.api.process_orders_with_invoices(this.tasks);

                    if (result.status === 'success') {
                        this.updateStatus(result.message);

                        // 更新统计信息
                        const successCount = result.success_count || 0;
                        const skipCount = result.skip_count || 0;
                        const errorCount = result.error_count || 0;

                        // 模拟任务状态更新（因为新模式是批量处理）
                        this.simulateTaskStatusUpdates(successCount, skipCount, errorCount);

                    } else {
                        this.updateStatus('上传失败: ' + result.message, 'error');
                    }

                } catch (error) {
                    this.updateStatus('上传过程中出错: ' + error.message, 'error');
                }
            }

            simulateTaskStatusUpdates(successCount, skipCount, errorCount) {
                // 模拟更新任务状态，用于界面显示
                let processedCount = 0;

                // 标记成功的任务
                for (let i = 0; i < this.tasks.length && processedCount < successCount; i++) {
                    if (this.tasks[i].status !== 'success') {
                        this.updateTaskStatus(i, 'success', '上传成功');
                        processedCount++;
                    }
                }

                // 标记跳过的任务
                processedCount = 0;
                for (let i = 0; i < this.tasks.length && processedCount < skipCount; i++) {
                    if (this.tasks[i].status !== 'success' && this.tasks[i].status !== 'skipped') {
                        this.updateTaskStatus(i, 'skipped', '未找到匹配订单');
                        processedCount++;
                    }
                }

                // 标记失败的任务
                processedCount = 0;
                for (let i = 0; i < this.tasks.length && processedCount < errorCount; i++) {
                    if (this.tasks[i].status !== 'success' && this.tasks[i].status !== 'skipped' && this.tasks[i].status !== 'error') {
                        this.updateTaskStatus(i, 'error', '上传失败');
                        processedCount++;
                    }
                }

                // 更新进度条到100%
                this.updateProgressFixed(this.tasks.length, this.tasks.length);
                this.updateStatistics();
            }

            updateTaskStatus(index, status, message) {
                const taskElement = this.allTasksList.querySelector(`[data-index="${index}"]`);
                if (!taskElement) return;

                // 更新任务对象的状态
                if (this.tasks[index]) {
                    this.tasks[index].status = status;
                    this.tasks[index].message = message;
                }

                // 设置元素状态
                this.setTaskElementStatus(taskElement, status, message);

                // 如果任务完成（成功、失败或跳过），移动到已完成列表
                if (['success', 'error', 'skipped'].includes(status)) {
                    setTimeout(() => {
                        this.moveTaskToCompleted(index, status, message);
                    }, 1000); // 延迟1秒，让用户看到状态变化
                }
            }

            setTaskElementStatus(taskElement, status, message) {
                // 更新样式
                taskElement.className = `task-item bg-white border border-gray-200 rounded-lg p-4 mb-3 shadow-sm hover:shadow-md transition-all duration-200 task-${status}`;

                // 更新图标和状态
                const icon = taskElement.querySelector('.status-icon');
                const statusText = taskElement.querySelector('.task-status');
                const messageText = taskElement.querySelector('.task-message');

                switch (status) {
                    case 'processing':
                        icon.className = 'status-icon ri-loader-line animate-spin text-blue-500';
                        statusText.textContent = '处理中';
                        break;
                    case 'success':
                        icon.className = 'status-icon ri-check-line text-green-500';
                        statusText.textContent = '成功';
                        break;
                    case 'error':
                        icon.className = 'status-icon ri-close-line text-red-500';
                        statusText.textContent = '失败';
                        break;
                    case 'skipped':
                        icon.className = 'status-icon ri-skip-forward-line text-yellow-500';
                        statusText.textContent = '跳过';
                        break;
                }

                messageText.textContent = message;
                messageText.title = message; // 添加tooltip显示完整消息
            }

            moveTaskToCompleted(index, status, message) {
                const task = this.tasks[index];
                if (!task) return;

                // 添加完成时间
                task.completedAt = new Date().toISOString();
                task.status = status;
                task.message = message;

                // 移动到已完成列表
                this.completedTasks.unshift(task); // 添加到开头，最新的在前面

                // 从全部任务中移除
                this.tasks.splice(index, 1);

                // 添加动画效果
                const taskElement = this.allTasksList.querySelector(`[data-index="${index}"]`);
                if (taskElement) {
                    taskElement.classList.add('task-completed-animation');
                    setTimeout(() => {
                        // 重新渲染列表
                        this.renderTaskList();
                        this.updateStatistics();
                    }, 300);
                } else {
                    // 直接重新渲染
                    this.renderTaskList();
                    this.updateStatistics();
                }
            }

            updateProgress() {
                const progress = Math.round((this.currentTaskIndex / this.tasks.length) * 100);
                this.progressBar.style.width = `${progress}%`;
                this.progressText.textContent = `${progress}%`;

                this.updateStatus(`正在处理第 ${this.currentTaskIndex + 1} 个任务，共 ${this.tasks.length} 个`);
            }

            updateProgressFixed(currentIndex, totalTasks) {
                // 🚀 修复：使用固定的总任务数计算进度，避免因数组变化导致进度计算错误
                const progress = Math.round(((currentIndex + 1) / totalTasks) * 100);
                this.progressBar.style.width = `${progress}%`;
                this.progressText.textContent = `${progress}%`;

                this.updateStatus(`正在处理第 ${currentIndex + 1} 个任务，共 ${totalTasks} 个`);
            }

            updateStatistics() {
                const stats = this.calculateStatistics();
                this.totalCount.textContent = this.tasks.length + this.completedTasks.length;
                this.successCount.textContent = stats.success;
                this.errorCount.textContent = stats.error;
                this.skipCount.textContent = stats.skipped;
            }

            calculateStatistics() {
                const stats = { success: 0, error: 0, skipped: 0 };

                // 统计已完成任务
                this.completedTasks.forEach(task => {
                    if (task.status === 'success') stats.success++;
                    else if (task.status === 'error') stats.error++;
                    else if (task.status === 'skipped') stats.skipped++;
                });

                return stats;
            }

            pauseUpload() {
                this.isPaused = !this.isPaused;
                
                if (this.isPaused) {
                    this.pauseUploadBtn.innerHTML = '<i class="ri-play-line mr-1"></i>继续';
                    this.updateStatus('已暂停，点击继续按钮恢复上传');
                } else {
                    this.pauseUploadBtn.innerHTML = '<i class="ri-pause-line mr-1"></i>暂停';
                    this.updateStatus('继续处理任务...');
                }
            }

            finishUpload() {
                this.isRunning = false;
                this.isPaused = false;
                
                this.startUploadBtn.disabled = false;
                this.pauseUploadBtn.disabled = true;
                this.selectFolderBtn.disabled = false;
                
                const stats = this.calculateStatistics();
                this.updateStatus(`上传完成！成功: ${stats.success}, 失败: ${stats.error}, 跳过: ${stats.skipped}`);
                
                this.progressBar.style.width = '100%';
                this.progressText.textContent = '100%';
            }

            updateStatus(message, type = 'info') {
                const icons = {
                    info: 'ri-information-line',
                    error: 'ri-error-warning-line',
                    success: 'ri-check-line'
                };
                
                const colors = {
                    info: 'text-gray-600',
                    error: 'text-red-600',
                    success: 'text-green-600'
                };
                
                this.currentStatus.innerHTML = `<i class="${icons[type]} mr-1"></i>${message}`;
                this.currentStatus.className = `text-sm ${colors[type]}`;
            }

            sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }

        // 初始化上传器
        document.addEventListener('DOMContentLoaded', () => {
            new InvoiceUploader();
        });
    </script>
</body>
</html>
