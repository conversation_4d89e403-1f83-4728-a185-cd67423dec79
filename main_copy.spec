# -*- mode: python ; coding: utf-8 -*-

# 专门针对 main copy.py 的 PyInstaller 配置文件
# 解决 DrissionPage 导致的 pandas DLL 依赖问题

import os

block_cipher = None

a = Analysis(
    ['main copy.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('index.html', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'webview',
        'requests',
        'urllib3',
        'DrissionPage',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'tkinter',
        'tkinter.filedialog',
        'pyautogui',
        'pyperclip',
    ],
    hookspath=['.'],  # 使用当前目录的 hook 文件
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas',           # 排除 pandas 避免 DLL 问题
        'numpy',            # 排除 numpy 避免 DLL 问题
        'pandas.core',      # 排除 pandas 核心模块
        'pandas.core.frame',
        'pandas.core.generic',
        'pandas.core.window',
        'pandas.core.window.ewm',
        'pandas.core.groupby',
        'pandas.core.api',
        'matplotlib',       # 排除其他可能导致问题的科学计算库
        'scipy',
        'sklearn',
        'tensorflow',
        'torch',
        'openpyxl',         # 如果不需要 Excel 功能可以排除
        'xlsxwriter',
        'xlrd',
        'PIL',
        'cv2',
        'selenium',         # 排除 selenium 因为使用的是 DrissionPage
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='发票申请查询系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
