# -*- mode: python ; coding: utf-8 -*-

# Windows 7 兼容的PyInstaller配置文件
# 解决pandas DLL依赖问题

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('index.html', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'openpyxl',
        'openpyxl.utils',
        'dateutil.relativedelta',
        'urllib3',
        'requests',
        'json',
        'threading',
        'time',
        'os',
        'sys',
        'webview',
        'DrissionPage',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas',  # 排除pandas避免DLL问题
        'numpy',   # 排除numpy避免DLL问题
        'matplotlib',
        'scipy',
        'sklearn',
        'tensorflow',
        'torch',
        'PIL',
        'cv2',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='发票申请查询系统_Win7',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
