import webview
import requests
import json
from datetime import datetime
import threading
import time
import os
import sys
import urllib3
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver

# DrissionPage相关导入
try:
    from DrissionPage import ChromiumPage, ChromiumOptions
    DRISSION_AVAILABLE = True
except ImportError:
    DRISSION_AVAILABLE = False
    print("DrissionPage未安装，浏览器自动化功能将不可用")

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def get_resource_path(relative_path):
    """获取资源文件路径，兼容开发环境和打包环境"""
    try:
        # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
        print(f"运行在打包环境，资源路径: {base_path}")
    except AttributeError:
        # 开发环境
        base_path = os.path.abspath(".")
        print(f"运行在开发环境，资源路径: {base_path}")

    return os.path.join(base_path, relative_path)

def get_config_path():
    """获取配置文件路径，确保在用户目录下可写"""
    if hasattr(sys, '_MEIPASS'):
        # 打包环境：配置文件放在exe同目录下
        exe_dir = os.path.dirname(sys.executable)
        config_path = os.path.join(exe_dir, "config.json")
        print(f"打包环境配置文件路径: {config_path}")
    else:
        # 开发环境：配置文件放在当前目录
        config_path = os.path.join(os.path.abspath("."), "config.json")
        print(f"开发环境配置文件路径: {config_path}")

    return config_path

class CustomHTTPRequestHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，用于提供静态资源"""

    def __init__(self, *args, **kwargs):
        # 设置资源目录
        if hasattr(sys, '_MEIPASS'):
            # 打包环境
            self.directory = sys._MEIPASS
        else:
            # 开发环境
            self.directory = os.path.abspath(".")
        super().__init__(*args, directory=self.directory, **kwargs)

    def log_message(self, format, *args):
        # 禁用日志输出
        pass

def start_http_server():
    """启动HTTP服务器提供静态资源"""
    try:
        port = 8765
        handler = CustomHTTPRequestHandler
        httpd = HTTPServer(("localhost", port), handler)
        print(f"HTTP服务器启动在端口 {port}")
        httpd.serve_forever()
    except Exception as e:
        print(f"HTTP服务器启动失败: {str(e)}")

class BrowserManager:
    """浏览器管理类"""

    def __init__(self):
        self.page = None
        self.is_initialized = False

    def safe_js_click(self, element, page):
        """
        安全的JavaScript点击方法，解决JavaScript world错误
        """
        try:
            # 方法1: 使用元素的xpath重新获取并点击
            element_xpath = element.xpath
            js_code = f"""
            var element = document.evaluate('{element_xpath}', document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null).singleNodeValue;
            if (element) {{
                element.click();
                return 'success';
            }} else {{
                return 'element_not_found';
            }}
            """
            result = page.run_js(js_code)
            if result == 'success':
                print("✅ JavaScript xpath点击成功")
                return True
            else:
                print(f"⚠️ JavaScript xpath点击失败: {result}")

                # 方法2: 使用CSS选择器
                try:
                    css_selector = element.css_path
                    js_code2 = f"""
                    var element = document.querySelector('{css_selector}');
                    if (element) {{
                        element.click();
                        return 'success';
                    }} else {{
                        return 'element_not_found';
                    }}
                    """
                    result2 = page.run_js(js_code2)
                    if result2 == 'success':
                        print("✅ JavaScript CSS选择器点击成功")
                        return True
                    else:
                        print(f"⚠️ JavaScript CSS选择器点击失败: {result2}")
                except Exception as css_e:
                    print(f"⚠️ CSS选择器方法失败: {str(css_e)}")

                # 方法3: 使用通用的点击坐标
                try:
                    rect = element.rect
                    center_x = rect.x + rect.width // 2
                    center_y = rect.y + rect.height // 2
                    js_code3 = f"""
                    var element = document.elementFromPoint({center_x}, {center_y});
                    if (element) {{
                        element.click();
                        return 'success';
                    }} else {{
                        return 'element_not_found';
                    }}
                    """
                    result3 = page.run_js(js_code3)
                    if result3 == 'success':
                        print("✅ JavaScript坐标点击成功")
                        return True
                    else:
                        print(f"⚠️ JavaScript坐标点击失败: {result3}")
                except Exception as coord_e:
                    print(f"⚠️ 坐标点击方法失败: {str(coord_e)}")

        except Exception as js_e:
            print(f"⚠️ JavaScript点击方法失败: {str(js_e)}")

        # 最后的回退方法
        try:
            page.run_js(f"arguments[0].click();", element)
            print("✅ 回退JavaScript点击成功")
            return True
        except Exception as fallback_e:
            print(f"⚠️ 回退JavaScript点击也失败: {str(fallback_e)}")
            return False

    def init_browser(self, cookie_string):
        """初始化浏览器"""
        if not DRISSION_AVAILABLE:
            return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

        try:
            # 强制重新创建浏览器实例
            if self.page is not None:
                try:
                    self.page.quit()
                except:
                    pass
                self.page = None
                self.is_initialized = False

            # 配置浏览器选项
            options = ChromiumOptions()
            options.set_argument('--no-sandbox')
            options.set_argument('--disable-dev-shm-usage')
            options.set_argument('--disable-gpu')
            options.set_argument('--remote-debugging-port=9222')
            options.set_argument('--disable-blink-features=AutomationControlled')
            options.set_argument('--disable-extensions')

            # 固定使用项目目录中的Chrome
            chrome_path = self._get_chrome_path()
            if chrome_path:
                print(f"使用固定Chrome路径: {chrome_path}")
                options.set_browser_path(chrome_path)
            else:
                print("❌ 未找到Chrome，请确保chrome\\App\\chrome.exe存在")
                return {"status": "error", "message": "未找到Chrome浏览器，请确保chrome\\App\\chrome.exe文件存在"}

            # 创建新的浏览器页面
            self.page = ChromiumPage(addr_or_opts=options)
            print("浏览器已重新创建")

            # 等待浏览器完全启动
            import time
            time.sleep(2)

            # 设置Cookie
            if cookie_string:
                self.set_cookies(cookie_string)

            self.is_initialized = True
            return {"status": "success", "message": "浏览器初始化成功"}

        except Exception as e:
            print(f"浏览器初始化失败: {str(e)}")
            # 确保状态重置
            self.page = None
            self.is_initialized = False
            return {"status": "error", "message": f"浏览器初始化失败: {str(e)}"}

    def _get_chrome_path(self):
        """获取Chrome路径，固定使用chrome\App\chrome.exe"""
        try:
            # 区分开发环境和打包环境（与配置文件路径逻辑保持一致）
            if hasattr(sys, '_MEIPASS'):
                # 打包环境：chrome目录在exe同级目录（不是临时目录）
                exe_dir = os.path.dirname(sys.executable)
                chrome_exe_path = os.path.join(exe_dir, "chrome", "App", "chrome.exe")
                print(f"打包环境Chrome路径: {chrome_exe_path}")
            else:
                # 开发环境：chrome目录在项目根目录
                project_dir = os.path.abspath(".")
                chrome_exe_path = os.path.join(project_dir, "chrome", "App", "chrome.exe")
                print(f"开发环境Chrome路径: {chrome_exe_path}")

            # 检查chrome.exe是否存在
            if os.path.exists(chrome_exe_path):
                print(f"✅ 找到Chrome: {chrome_exe_path}")
                return chrome_exe_path
            else:
                print(f"❌ Chrome不存在: {chrome_exe_path}")
                return None

        except Exception as e:
            print(f"获取Chrome路径失败: {str(e)}")
            return None

    def set_cookies(self, cookie_string):
        """设置Cookie"""
        try:
            if not self.page:
                return False

            # 先访问目标域名
            self.page.get('https://tgc.tmall.com')

            # 解析Cookie字符串并设置
            cookies = self.parse_cookie_string(cookie_string)
            for cookie in cookies:
                try:
                    self.page.set.cookies(cookie)
                except Exception as e:
                    print(f"设置Cookie失败: {cookie}, 错误: {str(e)}")

            print("Cookie设置完成")
            return True

        except Exception as e:
            print(f"设置Cookie失败: {str(e)}")
            return False

    def parse_cookie_string(self, cookie_string):
        """解析Cookie字符串"""
        cookies = []
        try:
            cookie_pairs = cookie_string.split(';')
            for pair in cookie_pairs:
                if '=' in pair:
                    name, value = pair.strip().split('=', 1)
                    cookies.append({
                        'name': name.strip(),
                        'value': value.strip(),
                        'domain': '.tmall.com'
                    })
        except Exception as e:
            print(f"解析Cookie失败: {str(e)}")

        return cookies

    def navigate_to_order_manage(self):
        """跳转到订单管理页面"""
        try:
            if not self.page:
                return {"status": "error", "message": "浏览器未初始化"}

            target_url = "https://tgc.tmall.com/ds/page/supplier/order-manage"
            self.page.get(target_url)

            # 等待页面加载
            time.sleep(2)

            return {"status": "success", "message": f"已跳转到订单管理页面: {target_url}"}

        except Exception as e:
            print(f"页面跳转失败: {str(e)}")
            return {"status": "error", "message": f"页面跳转失败: {str(e)}"}

    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.quit()
                self.page = None
                self.is_initialized = False
                print("浏览器已关闭")
        except Exception as e:
            print(f"关闭浏览器失败: {str(e)}")

    def is_browser_alive(self):
        """检查浏览器是否还活着"""
        if not self.is_initialized or not self.page:
            return False

        try:
            # 尝试获取当前URL来测试浏览器连接
            _ = self.page.url
            return True
        except Exception as e:
            print(f"浏览器连接已断开: {str(e)}")
            # 重置状态
            self.is_initialized = False
            self.page = None
            return False

class InvoiceAPI:
    def __init__(self):
        self.cookie = ""
        self.base_url = "https://tgc.tmall.com/ds/api/v1/invoice/supplier/apply/pageQueryInvoiceApply"
        # 初始化时创建配置文件
        self.init_config_file()
        # 加载并缓存规格映射配置
        self.spec_mappings = self.load_spec_mappings_cache()
        # 自动加载保存的Cookie
        self.load_saved_cookie()
        # 初始化浏览器管理器
        self.browser_manager = BrowserManager()

    def clean_cookie_string(self, cookie_str):
        """清理Cookie字符串，移除可能导致编码问题的字符"""
        if not isinstance(cookie_str, str):
            return str(cookie_str)

        try:
            import re
            # 移除所有可能导致JSON编码问题的字符
            # 移除控制字符、特殊Unicode符号等
            cleaned = re.sub(r'[\x00-\x1F\x7F-\x9F\u2000-\u206F\u2700-\u27BF\uFE00-\uFE0F\uFFF0-\uFFFF]', '', cookie_str)

            # 再次确保移除特定的问题字符
            cleaned = cleaned.replace('\u274c', '')  # 移除X符号
            cleaned = cleaned.replace('\u2713', '')  # 移除勾符号
            cleaned = cleaned.replace('\u2714', '')  # 移除勾符号

            # 只保留ASCII可打印字符和基本的URL编码字符
            cleaned = ''.join(c for c in cleaned if (32 <= ord(c) <= 126) or c in '=;,._-+/%&?')

            return cleaned.strip()
        except Exception as e:
            print(f"清理Cookie字符串失败: {str(e)}")
            # 如果清理失败，只保留ASCII字符
            try:
                return ''.join(c for c in cookie_str if 32 <= ord(c) <= 126)
            except:
                return ""

    def load_saved_cookie(self):
        """从配置文件加载保存的Cookie"""
        try:
            config_file = get_config_path()
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    saved_cookie = config.get('saved_cookie', '')
                    if saved_cookie:
                        # 使用清理函数确保Cookie有效
                        cleaned_cookie = self.clean_cookie_string(saved_cookie)
                        if cleaned_cookie:
                            self.cookie = cleaned_cookie
                            print("已自动加载保存的Cookie")
                        else:
                            print("保存的Cookie无效，已忽略")
                    else:
                        print("未找到保存的Cookie")
            else:
                print("配置文件不存在，无法加载Cookie")
        except Exception as e:
            print(f"加载Cookie失败: {str(e)}")

    def save_cookie_to_config(self, cookie):
        """保存Cookie到配置文件"""
        try:
            config_file = get_config_path()
            config = {}

            # 如果配置文件存在，先读取现有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 再次清理Cookie字符串，确保可以JSON序列化
            cleaned_cookie = self.clean_cookie_string(cookie)

            # 更新Cookie
            config['saved_cookie'] = cleaned_cookie

            # 保存配置，使用ensure_ascii=True避免编码问题
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=True, indent=2)

            print(f"✅ Cookie已保存到配置文件: {config_file}")
            print(f"✅ Cookie长度: {len(cleaned_cookie)} 字符")
            print(f"✅ 配置文件大小: {os.path.getsize(config_file)} 字节")
        except Exception as e:
            print(f"❌ 保存Cookie失败: {str(e)}")

    def init_config_file(self):
        """初始化配置文件"""
        try:
            config_file = get_config_path()
            if not os.path.exists(config_file):
                default_config = {
                    "spec_mappings": [],
                    "saved_cookie": "",
                    "riskbird_cookie": ""
                }
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=True, indent=2)
                print(f"配置文件已创建: {config_file}")
        except Exception as e:
            print(f"创建配置文件失败: {str(e)}")

    def load_spec_mappings_cache(self):
        """加载规格映射配置到缓存"""
        try:
            config_result = self.load_spec_config()
            if config_result.get('status') == 'success':
                mappings = config_result.get('data', [])
                print(f"已加载 {len(mappings)} 个规格映射配置")
                return mappings
            else:
                print("加载规格映射配置失败")
                return []
        except Exception as e:
            print(f"加载规格映射缓存失败: {str(e)}")
            return []

    def refresh_spec_mappings(self):
        """刷新规格映射缓存"""
        self.spec_mappings = self.load_spec_mappings_cache()
        return {"status": "success", "message": f"已刷新规格映射缓存，共 {len(self.spec_mappings)} 条"}

    def get_saved_cookie(self):
        """获取保存的Cookie"""
        try:
            return {"status": "success", "cookie": self.cookie}
        except Exception as e:
            return {"status": "error", "message": f"获取Cookie失败: {str(e)}", "cookie": ""}

    def set_cookie(self, cookie):
        """设置Cookie并保存到配置文件"""
        try:
            # 使用专门的清理函数处理Cookie
            cleaned_cookie = self.clean_cookie_string(cookie)

            if not cleaned_cookie:
                return {"status": "error", "message": "Cookie内容为空或无效"}

            self.cookie = cleaned_cookie
            # 保存Cookie到配置文件
            self.save_cookie_to_config(cleaned_cookie)
            return {"status": "success", "message": "Cookie设置成功"}
        except Exception as e:
            return {"status": "error", "message": f"Cookie设置失败: {str(e)}"}
    
    def get_invoice_data(self, page=1, page_size=10):
        """获取发票数据"""
        if not self.cookie:
            return {"status": "error", "message": "请先设置Cookie"}

        # 确保规格映射配置是最新的
        print(f"当前缓存的规格映射数量: {len(self.spec_mappings)}")
        for i, mapping in enumerate(self.spec_mappings):
            print(f"映射 {i+1}: '{mapping.get('productName', '')}' → '{mapping.get('specModel', '')}'")

        def get_mapped_specification(item_name, original_spec):
            """根据项目名称获取映射的规格型号"""

            if not item_name or not self.spec_mappings:
                return '包'  # 不匹配时返回默认规格"包"

            # 遍历所有映射配置，查找匹配的关键词
            for mapping in self.spec_mappings:
                keyword = mapping.get('productName', '')
                mapped_spec = mapping.get('specModel', '')

                # 如果项目名称包含关键词，则使用映射的规格
                if keyword and mapped_spec and keyword in item_name:
                    return mapped_spec
            return '包'

        # 确保Cookie字符串是安全的，避免编码问题
        safe_cookie = self.clean_cookie_string(self.cookie) if self.cookie else ""

        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json;charset=UTF-8',
            'Cookie': safe_cookie,
            'Host': 'tgc.tmall.com',
            'Origin': 'https://tgc.tmall.com',
            'Referer': 'https://tgc.tmall.com/ds/page/supplier/finance-invoice?c2mNavigatorShellPage=0&c2mNavigatorPageOpener=',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'bx-v': '2.5.31',
            'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        }

        # 计算日期范围（最近3个月）
        from dateutil.relativedelta import relativedelta
        end_date = datetime.now()
        start_date = end_date - relativedelta(months=3)
        
        payload = {
            "supplierIdSec": 1000000000198050,
            "title": "",
            "invoiceKindArray": "",
            "invoiceTypeArray": "",
            "applyStartDateStr": start_date.strftime("%Y-%m-%d 00:00:00"),
            "applyEndDateStr": end_date.strftime("%Y-%m-%d 23:59:59"),
            "invoicingStartDate": "",
            "invoicingEndDate": "",
            "claimsType": "",
            "invoice2cTabStatus": 0,
            "page": page,
            "pageSize": page_size,
            "isPunish": True,
            "pageParam": {"page": page, "size": page_size},
            "sorters": [{"sortColumn": "claims_deadline", "sortType": "ASC"}],
            "supplierIdSecList": [1000000000198050]
        }
        
        try:
            # 首先获取第一页数据，确定总数
            response = requests.post(self.base_url, headers=headers, json=payload, timeout=30, verify=False)
            response.raise_for_status()

            data = response.json()
            if not data.get('success'):
                return {"status": "error", "message": f"API返回错误: {data.get('errorMessage', '未知错误')}"}

            total_records = data.get('paginator', {}).get('total', 0)

            # 如果总记录数大于当前页大小，则获取所有数据
            if total_records > page_size:
                # 重新请求，获取所有数据
                payload['pageSize'] = total_records
                payload['pageParam']['size'] = total_records

                response = requests.post(self.base_url, headers=headers, json=payload, timeout=60, verify=False)
                response.raise_for_status()
                data = response.json()

                if not data.get('success'):
                    return {"status": "error", "message": f"API返回错误: {data.get('errorMessage', '未知错误')}"}

            # 处理数据，添加格式化的时间和序号
            invoice_items_dict = {}  # 用于合并相同orderId的记录

            for item in data.get('dataList', []):
                order_id = item.get('orderId', '')

                # 收集商品信息
                item_names = []
                specifications = []
                quantities = []

                for sub_order in item.get('subOrderDTOList', []):
                    # 收集商品信息
                    auction_title = sub_order.get('auctionTitle', '')
                    if auction_title:
                        item_names.append(auction_title)

                    # 获取原始规格和映射后的规格
                    original_spec = sub_order.get('featureSaleSpec', '')
                    mapped_spec = get_mapped_specification(auction_title, original_spec)
                    # 添加映射后的规格（包括默认的"包"）
                    if mapped_spec and mapped_spec.strip():
                        specifications.append(mapped_spec)

                    buy_amount = sub_order.get('buyAmount', 0)
                    if buy_amount:
                        quantities.append(str(buy_amount))

                for invoice in item.get('invoiceList', []):
                    # 格式化申请时间
                    if invoice.get('applyTime'):
                        invoice['applyTimeFormatted'] = datetime.fromtimestamp(
                            invoice['applyTime'] / 1000
                        ).strftime('%Y-%m-%d %H:%M:%S')

                    # 格式化截止时间
                    if invoice.get('claimsDeadline'):
                        invoice['claimsDeadlineFormatted'] = datetime.fromtimestamp(
                            invoice['claimsDeadline'] / 1000
                        ).strftime('%Y-%m-%d %H:%M:%S')

                    # 格式化金额（分转元）
                    if invoice.get('invoiceAmount'):
                        invoice['invoiceAmountFormatted'] = f"¥{invoice['invoiceAmount'] / 100:.2f}"

                    if invoice.get('originalPayAmount'):
                        invoice['originalPayAmountFormatted'] = f"¥{invoice['originalPayAmount'] / 100:.2f}"

                    # 获取第一个发票项目的bizOrderId作为发票流水号
                    biz_order_id = ''
                    if invoice.get('invoiceItemDTOList'):
                        biz_order_id = invoice['invoiceItemDTOList'][0].get('bizOrderId', '')

                    # 获取发票金额（分转元）
                    invoice_amount = invoice.get('invoiceAmount', 0)
                    amount_value = invoice_amount / 100 if invoice_amount else 0

                    if order_id in invoice_items_dict:
                        # 如果orderId已存在，更新金额（不累计，直接使用最新的开票金额）
                        existing_item = invoice_items_dict[order_id]

                        existing_item['amount'] = str(amount_value)
                        existing_item['amountFormatted'] = f"¥{amount_value:.2f}"

                        # 累计数量
                        existing_quantity = 0
                        try:
                            if existing_item['quantity'] and str(existing_item['quantity']).replace('.', '').isdigit():
                                existing_quantity = int(float(existing_item['quantity']))
                        except (ValueError, TypeError):
                            existing_quantity = 0

                        current_total_quantity = sum(int(q) for q in quantities if q.isdigit())
                        total_quantity = existing_quantity + current_total_quantity
                        existing_item['quantity'] = str(total_quantity)

                        # 合并项目名称
                        for item_name in item_names:
                            if item_name and item_name not in existing_item['itemName']:
                                existing_item['itemName'] += f"; {item_name}"

                        # 合并规格型号（去重）
                        existing_specs = set()
                        if existing_item['specification'] and existing_item['specification'].strip():
                            existing_specs = set(existing_item['specification'].split('; '))

                        for spec in specifications:
                            if spec and spec.strip():
                                existing_specs.add(spec)

                        if existing_specs:
                            existing_item['specification'] = '; '.join(sorted(existing_specs))
                        else:
                            existing_item['specification'] = ''
                    else:
                        # 新的orderId，创建新记录
                        total_quantity = sum(int(q) for q in quantities if q.isdigit())

                        # 处理规格型号（去重并排序）
                        unique_specs = set(spec for spec in specifications if spec and spec.strip())
                        final_specification = '; '.join(sorted(unique_specs)) if unique_specs else ''

                        # 处理发票抬头 - 个人用户留空，企业用户使用原始标题
                        original_title = invoice.get('invoiceTitle', '')
                        payer_register_no = invoice.get('payerRegisterNo', '')

                        # 判断是否为个人（没有纳税人识别号或为空）
                        is_personal = not payer_register_no or payer_register_no.strip() == ''

                        if is_personal:
                            # 个人用户，发票抬头留空，等待用户手动获取
                            invoice_title = ''
                            print(f"检测到个人发票，订单号: {order_id}，发票抬头留空等待手动获取")
                        else:
                            # 企业用户，使用原始标题
                            invoice_title = original_title

                        invoice_detail = {
                            'bizOrderId': biz_order_id,
                            'invoiceTitle': invoice_title,
                            'payerRegisterNo': payer_register_no,
                            'itemName': '; '.join(item_names) if item_names else '',
                            'specification': final_specification,
                            'quantity': str(total_quantity),
                            'amount': str(amount_value),
                            'amountFormatted': f"¥{amount_value:.2f}",
                            'orderId': order_id,
                            'supplierNickSec': item.get('supplierNickSec', ''),
                            'applyTimeFormatted': invoice.get('applyTimeFormatted', ''),
                            'source': invoice.get('source', ''),
                            'selected': True  # 默认选中
                        }
                        invoice_items_dict[order_id] = invoice_detail
                    break  # 每个orderId只处理一次

            # 转换为列表并添加序号
            all_invoice_items = []
            sequence_number = 1
            for invoice_detail in invoice_items_dict.values():
                invoice_detail['sequence'] = sequence_number
                all_invoice_items.append(invoice_detail)
                sequence_number += 1

            # 更新数据结构
            data['invoiceItems'] = all_invoice_items
            data['totalItems'] = len(all_invoice_items)

            return {"status": "success", "data": data}

        except requests.exceptions.RequestException as e:
            return {"status": "error", "message": f"网络请求失败: {str(e)}"}
        except json.JSONDecodeError as e:
            return {"status": "error", "message": f"JSON解析失败: {str(e)}"}
        except Exception as e:
            return {"status": "error", "message": f"未知错误: {str(e)}"}

    def export_to_excel(self, selected_items):
        """导出选中的数据到Excel"""
        try:
            if not selected_items:
                return {"status": "error", "message": "没有选中的数据"}

            # 导入openpyxl
            from openpyxl import Workbook
            from openpyxl.utils import get_column_letter

            # 准备Excel数据
            headers = [
                '*发票流水号', '购买方名称(抬头)', '购买方纳税人识别号',
                '项目名称', '规格型号', '数量', '金额'
            ]

            excel_data = []
            for item in selected_items:
                row_data = [
                    item.get('orderId', ''),
                    item.get('invoiceTitle', ''),
                    item.get('payerRegisterNo', ''),
                    item.get('itemName', ''),
                    item.get('specification', ''),
                    item.get('quantity', ''),
                    item.get('amount', '')
                ]
                excel_data.append(row_data)

            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = '发票申请数据'

            # 写入表头
            for col_num, header in enumerate(headers, 1):
                ws.cell(row=1, column=col_num, value=header)

            # 写入数据
            for row_num, row_data in enumerate(excel_data, 2):
                for col_num, value in enumerate(row_data, 1):
                    ws.cell(row=row_num, column=col_num, value=value)

            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"发票申请数据_{timestamp}.xlsx"

            # 保存到桌面
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            if not os.path.exists(desktop_path):
                desktop_path = os.path.expanduser("~")  # 如果桌面不存在，保存到用户目录

            file_path = os.path.join(desktop_path, filename)

            # 保存Excel文件
            wb.save(file_path)

            return {"status": "success", "message": f"数据已导出到: {file_path}", "file_path": file_path}

        except Exception as e:
            return {"status": "error", "message": f"导出失败: {str(e)}"}

    def load_spec_config(self):
        """加载规格配置"""
        try:
            config_file = get_config_path()
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return {"status": "success", "data": config.get('spec_mappings', [])}
            else:
                return {"status": "success", "data": []}
        except Exception as e:
            return {"status": "error", "message": f"加载配置失败: {str(e)}"}

    def save_spec_config(self, spec_mappings):
        """保存规格配置"""
        try:
            config_file = get_config_path()
            config = {}

            # 如果配置文件存在，先读取现有配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新规格映射配置
            config['spec_mappings'] = spec_mappings

            # 保存配置，使用ensure_ascii=True避免编码问题
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=True, indent=2)

            return {"status": "success", "message": "配置保存成功"}
        except Exception as e:
            return {"status": "error", "message": f"保存配置失败: {str(e)}"}

    def add_spec_mapping(self, product_name, spec_model):
        """添加单个规格映射并立即保存"""
        try:
            # 加载现有配置
            config_result = self.load_spec_config()
            if config_result.get('status') != 'success':
                return config_result

            spec_mappings = config_result.get('data', [])

            # 检查是否已存在
            exists = any(
                mapping.get('productName') == product_name and mapping.get('specModel') == spec_model
                for mapping in spec_mappings
            )

            if exists:
                return {"status": "error", "message": "该映射关系已存在"}

            # 添加新映射
            spec_mappings.append({
                "productName": product_name,
                "specModel": spec_model
            })

            # 保存配置
            save_result = self.save_spec_config(spec_mappings)
            if save_result.get('status') == 'success':
                # 更新缓存
                self.spec_mappings = spec_mappings
                print(f"映射添加成功: {product_name} → {spec_model}")
                return {"status": "success", "message": "映射添加成功", "data": spec_mappings}
            else:
                return save_result

        except Exception as e:
            return {"status": "error", "message": f"添加映射失败: {str(e)}"}

    def remove_spec_mapping(self, index):
        """删除指定索引的规格映射并立即保存"""
        try:
            # 加载现有配置
            config_result = self.load_spec_config()
            if config_result.get('status') != 'success':
                return config_result

            spec_mappings = config_result.get('data', [])

            # 检查索引是否有效
            if index < 0 or index >= len(spec_mappings):
                return {"status": "error", "message": "无效的映射索引"}

            # 删除映射
            spec_mappings.pop(index)

            # 保存配置
            save_result = self.save_spec_config(spec_mappings)
            if save_result.get('status') == 'success':
                # 更新缓存
                self.spec_mappings = spec_mappings
                print(f"映射删除成功: 索引 {index}")
                return {"status": "success", "message": "映射删除成功", "data": spec_mappings}
            else:
                return save_result

        except Exception as e:
            return {"status": "error", "message": f"删除映射失败: {str(e)}"}

    def get_recipient_info(self, order_id):
        """获取收件人信息（个人用户获取真实姓名）"""
        try:
            # 首先尝试获取个人用户真实姓名
            print(f"开始获取订单 {order_id} 的收件人信息...")
            user_info = self.get_personal_user_name(order_id)

            if user_info.get('status') == 'success' and user_info.get('fullName'):
                # 成功获取到用户信息
                full_name = user_info.get('fullName')
                buyer_nick = user_info.get('buyerNick', '')
                mobile_phone = user_info.get('mobilephone', '')
                new_cookie = user_info.get('new_cookie')  # 如果有新Cookie

                print(f"成功获取收件人信息: {full_name}")

                result = {
                    "status": "success",
                    "message": f"成功获取收件人信息: {full_name}",
                    "order_id": order_id,
                    "fullName": full_name,
                    "buyerNick": buyer_nick,
                    "mobilephone": mobile_phone,
                    "action": "update_name"  # 标识需要更新姓名
                }

                # 如果有新Cookie，添加到返回结果中
                if new_cookie:
                    result["new_cookie"] = new_cookie
                    result["message"] += "（Cookie已更新）"

                return result
            else:
                # 获取失败，回退到浏览器跳转
                print(f"API获取失败，尝试浏览器跳转方式...")

                if not DRISSION_AVAILABLE:
                    return {"status": "error", "message": "无法获取收件人信息：API调用失败且DrissionPage未安装"}

                # 检查浏览器状态并初始化（如果需要）
                if not self.browser_manager.is_browser_alive():
                    init_result = self.browser_manager.init_browser(self.cookie)
                    if init_result["status"] != "success":
                        return init_result

                # 跳转到订单管理页面
                nav_result = self.browser_manager.navigate_to_order_manage()
                if nav_result["status"] != "success":
                    return nav_result

                return {
                    "status": "success",
                    "message": f"API获取失败，已跳转到订单管理页面，请手动查找订单号: {order_id}",
                    "order_id": order_id,
                    "action": "browser_navigate"  # 标识为浏览器跳转
                }

        except Exception as e:
            return {"status": "error", "message": f"获取收件人信息失败: {str(e)}"}

    def get_identification_code(self, order_id):
        """获取识别码信息"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

            # 初始化浏览器（如果还没有初始化）
            if not self.browser_manager.is_initialized:
                init_result = self.browser_manager.init_browser(self.cookie)
                if init_result["status"] != "success":
                    return init_result

            # 跳转到订单管理页面
            nav_result = self.browser_manager.navigate_to_order_manage()
            if nav_result["status"] != "success":
                return nav_result

            # 这里可以添加具体的识别码获取逻辑
            # 例如：搜索订单号，获取识别码等

            return {
                "status": "success",
                "message": f"已跳转到订单管理页面，请手动查找订单号: {order_id}",
                "order_id": order_id
            }

        except Exception as e:
            return {"status": "error", "message": f"获取识别码失败: {str(e)}"}

    def get_personal_user_name(self, order_id):
        """获取个人用户真实姓名"""
        try:
            if not self.cookie:
                return {"status": "error", "message": "Cookie未设置，无法获取用户信息"}

            url = "https://tgc.tmall.com/ds/api/v1/o/qOsiWithRecord"

            # 构建请求头
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-encoding': 'gzip, deflate, br, zstd',
                'accept-language': 'zh-CN,zh;q=0.9',
                'bx-v': '2.5.31',
                'content-type': 'application/json;charset=UTF-8',
                'origin': 'https://tgc.tmall.com',
                'referer': 'https://tgc.tmall.com/ds/page/supplier/order-manage',
                'sec-ch-ua': '"Google Chrome";v="135", "Not-A.Brand";v="8", "Chromium";v="135"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'cookie': self.cookie
            }

            # 从Cookie中提取XSRF-TOKEN
            xsrf_token = self.extract_xsrf_token(self.cookie)
            if xsrf_token:
                headers['x-xsrf-token'] = xsrf_token

            # 请求体
            payload = {
                "mainOrderId": order_id,
                "infoKeys": ["fullName", "mobilephone", "buyerNick"]
            }

            print(f"正在获取订单 {order_id} 的用户信息...")

            response = requests.post(
                url=url,
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                print(result)
                if result.get('success'):
                    data = result.get('data', {})
                    full_name = data.get('fullName', '')
                    buyer_nick = data.get('buyerNick', '')
                    mobile_phone = data.get('mobilephone', '')

                    print(f"成功获取用户信息: {full_name}")

                    return {
                        "status": "success",
                        "fullName": full_name,
                        "buyerNick": buyer_nick,
                        "mobilephone": mobile_phone
                    }
                else:
                    error_msg = result.get('errorMessage', '获取用户信息失败')
                    ret_codes = result.get('ret', [])

                    # 检查是否是"被挤爆啦"错误
                    if any('被挤爆啦' in str(code) for code in ret_codes):
                        print(f"检测到验证码挑战: {error_msg}")
                        # 获取验证URL
                        captcha_url = result.get('data', {}).get('url', '')
                        if captcha_url:
                            print(f"验证URL: {captcha_url}")
                            # 触发浏览器验证
                            captcha_result = self.handle_captcha_challenge(captcha_url)
                            if captcha_result.get('status') == 'success':
                                # 验证成功，使用新Cookie重试
                                print("验证成功，使用新Cookie重试获取用户信息...")
                                retry_result = self.get_personal_user_name(order_id)
                                # 如果重试成功，添加新Cookie信息
                                if retry_result.get('status') == 'success':
                                    retry_result['new_cookie'] = captcha_result.get('new_cookie')
                                return retry_result
                            else:
                                return captcha_result
                        else:
                            return {"status": "error", "message": "需要验证但未获取到验证URL"}

                    print(f"API返回错误: {error_msg}")
                    return {"status": "error", "message": error_msg}
            else:
                print(f"HTTP请求失败: {response.status_code}")
                return {"status": "error", "message": f"HTTP请求失败: {response.status_code}"}

        except Exception as e:
            print(f"获取用户信息异常: {str(e)}")
            return {"status": "error", "message": f"获取用户信息失败: {str(e)}"}

    def handle_captcha_challenge(self, captcha_url):
        """处理验证码挑战"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法处理验证码"}

            print("开始处理验证码挑战...")

            # 检查浏览器状态并初始化（如果需要）
            if not self.browser_manager.is_browser_alive():
                init_result = self.browser_manager.init_browser(self.cookie)
                if init_result["status"] != "success":
                    return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面未初始化"}

            # 植入当前Cookie
            print("植入Cookie...")
            self.browser_manager.set_cookies(self.cookie)

            # 刷新页面
            print("刷新页面...")
            page.refresh()

            # 打开验证URL
            print(f"打开验证URL: {captcha_url}")
            page.get(captcha_url)

            print("等待用户手动过滑块验证...")
            print("请在浏览器中完成验证，系统将自动检测验证完成...")

            # 循环检测地址是否变成 https://www.tmall.com/
            max_wait_time = 300  # 最多等待5分钟
            check_interval = 2   # 每2秒检查一次
            elapsed_time = 0

            while elapsed_time < max_wait_time:
                try:
                    current_url = page.url
                    print(f"当前URL: {current_url}")

                    if current_url and 'www.tmall.com' in current_url:
                        print("检测到验证成功，正在获取新Cookie...")

                        # 获取当前*.tmall.com的所有Cookie
                        new_cookies = self.get_tmall_cookies(page)
                        if new_cookies:
                            # 保存新Cookie到配置文件
                            self.save_new_cookie(new_cookies)

                            # 更新当前实例的Cookie
                            self.cookie = new_cookies

                            print("Cookie已更新并保存")

                            # 关闭浏览器
                            try:
                                self.browser_manager.close_browser()
                                print("浏览器已关闭")
                            except Exception as e:
                                print(f"关闭浏览器失败: {str(e)}")

                            return {
                                "status": "success",
                                "message": "验证成功，Cookie已更新",
                                "new_cookie": new_cookies
                            }
                        else:
                            # 关闭浏览器
                            try:
                                self.browser_manager.close_browser()
                                print("浏览器已关闭")
                            except Exception as e:
                                print(f"关闭浏览器失败: {str(e)}")
                            return {"status": "error", "message": "验证成功但获取Cookie失败"}

                    # 等待一段时间再检查
                    import time
                    time.sleep(check_interval)
                    elapsed_time += check_interval

                except Exception as e:
                    print(f"检测过程中出错: {str(e)}")
                    time.sleep(check_interval)
                    elapsed_time += check_interval

            # 验证超时，关闭浏览器
            try:
                self.browser_manager.close_browser()
                print("验证超时，浏览器已关闭")
            except Exception as e:
                print(f"关闭浏览器失败: {str(e)}")

            return {"status": "error", "message": "验证超时，请重试"}

        except Exception as e:
            print(f"处理验证码挑战失败: {str(e)}")
            return {"status": "error", "message": f"处理验证码挑战失败: {str(e)}"}

    def get_tmall_cookies(self, page):
        """获取*.tmall.com域名下的所有Cookie"""
        try:
            all_cookies = page.cookies()
            tmall_cookies = []

            for cookie in all_cookies:
                domain = cookie.get('domain', '')
                if 'tmall.com' in domain:
                    name = cookie.get('name', '')
                    value = cookie.get('value', '')
                    if name and value:
                        tmall_cookies.append(f"{name}={value}")

            if tmall_cookies:
                cookie_string = '; '.join(tmall_cookies)
                print(f"获取到 {len(tmall_cookies)} 个tmall.com Cookie")
                return cookie_string
            else:
                print("未找到tmall.com相关Cookie")
                return None

        except Exception as e:
            print(f"获取Cookie失败: {str(e)}")
            return None

    def save_new_cookie(self, new_cookie):
        """保存新Cookie到配置文件"""
        try:
            # 使用统一的保存方法
            self.save_cookie_to_config(new_cookie)
            print("新Cookie已保存到配置文件")
            return True

        except Exception as e:
            print(f"保存Cookie失败: {str(e)}")
            return False

    def extract_xsrf_token(self, cookie_string):
        """从Cookie字符串中提取XSRF-TOKEN"""
        try:
            cookie_pairs = cookie_string.split(';')
            for pair in cookie_pairs:
                if 'XSRF-TOKEN=' in pair or 'X-XSRF-TOKEN=' in pair:
                    return pair.split('=', 1)[1].strip()
            return None
        except Exception as e:
            print(f"提取XSRF-TOKEN失败: {str(e)}")
            return None

    def get_current_cookie(self):
        """获取当前Cookie（供前端调用）"""
        return {"status": "success", "cookie": self.cookie}

    def auto_login(self):
        """自动登录功能：打开浏览器访问登录页面，监控URL变化并获取Cookie"""
        if not DRISSION_AVAILABLE:
            return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

        try:
            # 初始化登录状态
            self._login_status = {
                "success": False,
                "message": "正在初始化登录流程...",
                "timestamp": 0
            }

            # 登录页面URL
            login_url = "https://tgcwork.tmall.com/pages/ascm/basic_login?from=https%3A%2F%2Ftgc.tmall.com%2Fds%2Fpage%2Fsupplier%2Fapplication-invoice%3FtabKey%3DINVOICE_CONSUMER"
            # 目标页面URL（登录成功后的页面）
            target_url = "https://tgc.tmall.com/ds/page/supplier/application-invoice?tabKey=INVOICE_CONSUMER"

            # 初始化浏览器（不传Cookie）
            init_result = self.browser_manager.init_browser("")
            if init_result["status"] != "success":
                return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面初始化失败"}

            # 跳转到登录页面
            print(f"正在跳转到登录页面: {login_url}")
            page.get(login_url)

            # 等待页面加载
            import time
            time.sleep(3)

            # 启动URL监控线程
            import threading
            monitoring_thread = threading.Thread(
                target=self._monitor_login_url,
                args=(page, target_url),
                daemon=True
            )
            monitoring_thread.start()

            return {
                "status": "success",
                "message": "浏览器已打开登录页面，请完成登录。系统将自动检测登录状态并保存Cookie。",
                "login_url": login_url,
                "target_url": target_url
            }

        except Exception as e:
            print(f"自动登录失败: {str(e)}")
            return {"status": "error", "message": f"自动登录失败: {str(e)}"}

    def get_login_status(self):
        """获取登录状态（供前端轮询检查）"""
        try:
            if hasattr(self, '_login_status'):
                return {
                    "status": "success",
                    "data": self._login_status
                }
            else:
                return {
                    "status": "success",
                    "data": {
                        "success": False,
                        "message": "未开始登录流程",
                        "timestamp": 0
                    }
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"获取登录状态失败: {str(e)}"
            }

    def _monitor_login_url(self, page, target_url):
        """监控URL变化的后台线程"""
        try:
            import time
            max_attempts = 120  # 最多监控10分钟（120次 * 5秒）
            attempts = 0
            login_success = False

            print("开始监控登录状态...")

            while attempts < max_attempts:
                try:
                    # 检查浏览器是否还活着
                    if not self.browser_manager.is_browser_alive():
                        print("浏览器已关闭，停止监控")
                        break

                    # 获取当前URL
                    current_url = page.url
                    print(f"当前URL: {current_url}")

                    # 检查是否已经跳转到目标页面
                    if target_url in current_url:
                        print("✅ 检测到登录成功，等待3秒后获取Cookie...")

                        # 等待3秒，确保页面完全加载和Cookie设置完成
                        time.sleep(3)

                        print("正在获取Cookie...")
                        # 获取当前页面的所有Cookie
                        cookies = page.cookies()
                        if cookies:
                            # 将Cookie转换为字符串格式
                            cookie_string = self._format_cookies_to_string(cookies)

                            if cookie_string and len(cookie_string) > 100:  # 确保Cookie有效
                                # 保存Cookie
                                self.cookie = cookie_string
                                self.save_cookie_to_config(cookie_string)
                                print("✅ Cookie已自动获取并保存")

                                # 设置登录成功标志
                                login_success = True

                                # 关闭浏览器
                                print("正在关闭浏览器...")
                                try:
                                    self.browser_manager.close_browser()
                                    print("✅ 浏览器已关闭")
                                except Exception as e:
                                    print(f"关闭浏览器时出错: {str(e)}")

                                # 设置登录状态标志，供前端检查
                                self._login_status = {
                                    "success": True,
                                    "message": "登录成功！Cookie已自动获取并保存，浏览器已关闭。",
                                    "cookie_length": len(cookie_string),
                                    "timestamp": time.time()
                                }

                                break
                            else:
                                print("⚠️ Cookie无效或为空，继续监控...")
                        else:
                            print("⚠️ 未获取到Cookie，继续监控...")

                    # 等待5秒后再次检查
                    time.sleep(5)
                    attempts += 1

                except Exception as e:
                    print(f"监控过程中出错: {str(e)}")
                    time.sleep(5)
                    attempts += 1

            # 监控结束处理
            if attempts >= max_attempts:
                print("⚠️ 监控超时，请手动设置Cookie")
                self._login_status = {
                    "success": False,
                    "message": "登录监控超时，请手动完成登录或重试。",
                    "timestamp": time.time()
                }
                # 超时也关闭浏览器
                try:
                    self.browser_manager.close_browser()
                except:
                    pass
            elif not login_success:
                print("监控结束，未检测到登录成功")
                self._login_status = {
                    "success": False,
                    "message": "监控结束，未检测到登录成功。",
                    "timestamp": time.time()
                }

        except Exception as e:
            print(f"URL监控线程异常: {str(e)}")
            self._login_status = {
                "success": False,
                "message": f"监控过程中发生异常: {str(e)}",
                "timestamp": time.time()
            }

    def _format_cookies_to_string(self, cookies):
        """将Cookie列表格式化为字符串"""
        try:
            cookie_pairs = []
            for cookie in cookies:
                name = cookie.get('name', '')
                value = cookie.get('value', '')
                if name and value:
                    cookie_pairs.append(f"{name}={value}")

            cookie_string = '; '.join(cookie_pairs)
            return cookie_string if cookie_string else ""

        except Exception as e:
            print(f"格式化Cookie失败: {str(e)}")
            return ""

    def upload_invoice_file(self, file_path=""):
        """简化的发票文件上传方法"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

            if not self.cookie:
                return {"status": "error", "message": "Cookie未设置，请先设置Cookie"}

            print("=== 简化发票上传流程 ===")
            print("正在启动浏览器...")

            # 初始化浏览器
            init_result = self.browser_manager.init_browser(self.cookie)
            if init_result["status"] != "success":
                return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面初始化失败"}

            # 跳转到测试页面
            test_url = "https://tgc.tmall.com/ds/page/supplier/application-invoice?tabKey=INVOICE_CONSUMER"
            print(f"跳转到发票申请页面...")
            page.get(test_url)
            time.sleep(3)

            current_url = page.url
            # 更准确的页面加载判断：检查是否真的到达了目标页面
            if not current_url.startswith("https://tgc.tmall.com/ds/page/supplier/application-invoice"):
                # 检查是否被重定向到登录页面
                if "basic_login" in current_url:
                    return {"status": "error", "message": f"需要重新登录，当前被重定向到登录页面: {current_url}"}
                else:
                    return {"status": "error", "message": f"页面跳转失败或Cookie无效，当前URL: {current_url}"}

            print("页面加载成功，开始自动操作...")

            # 点击第一个发票录入按钮
            first_invoice_button_xpath = '//*[@id="root"]/div/div/div/div[2]/div/div/div/div[5]/div[2]/div/div[4]/div/div[2]/table/tbody/tr[1]/td/div/div/div[1]/button/span'
            first_button = page.ele(f'xpath:{first_invoice_button_xpath}')
            if not first_button:
                return {"status": "error", "message": "未找到发票录入按钮"}

            print("点击发票录入按钮...")
            first_button.click()
            time.sleep(2)

            # 查找弹窗
            modal_xpath = '/html/body/div[4]/div/div'
            modal = page.ele(f'xpath:{modal_xpath}')
            if not modal:
                return {"status": "error", "message": "录入发票弹窗未出现"}

            print("弹窗已打开，查找上传按钮...")

            # 查找上传按钮
            upload_elements = modal.eles('text:上传PDF')
            upload_button = None
            for elem in upload_elements:
                if elem.tag in ['button', 'label'] or 'btn' in (elem.attr('class') or ''):
                    upload_button = elem
                    break

            if not upload_button:
                return {"status": "error", "message": "未找到上传PDF按钮"}

            print("点击上传PDF按钮...")
            upload_button.click()
            time.sleep(1)

            # 处理文件上传
            if file_path and os.path.exists(file_path):
                print(f"自动上传文件: {file_path}")
                file_input = modal.ele('tag:input@type=file')
                if file_input:
                    file_input.input(file_path)
                    print("文件上传成功！")
                    time.sleep(2)
                    return {
                        "status": "success",
                        "message": f"发票文件上传成功: {os.path.basename(file_path)}",
                        "action": "file_uploaded",
                        "file_path": file_path
                    }
                else:
                    return {"status": "error", "message": "未找到文件输入框"}
            else:
                return {
                    "status": "success",
                    "message": "上传按钮已点击，请在文件对话框中选择PDF文件",
                    "action": "upload_ready"
                }

        except Exception as e:
            print(f"上传过程失败: {str(e)}")
            return {"status": "error", "message": f"上传过程失败: {str(e)}"}

    def scan_and_parse_pdfs(self, folder_path):
        """扫描文件夹并解析PDF文件名"""
        pdf_files = []
        try:
            print(f"=== 开始扫描文件夹 ===")
            print(f"文件夹路径: {folder_path}")

            if not os.path.exists(folder_path):
                print(f"❌ 文件夹不存在: {folder_path}")
                return pdf_files

            for filename in os.listdir(folder_path):
                if filename.lower().endswith('.pdf'):
                    print(f"找到PDF文件: {filename}")

                    # 解析文件名: dzfp_25442000000427607353_潍坊恒言酒店管理有限公司_20250716093438.pdf
                    try:
                        parts = filename.replace('.pdf', '').split('_')
                        if len(parts) >= 4 and parts[0] == 'dzfp':
                            file_info = {
                                'filename': filename,
                                'filepath': os.path.join(folder_path, filename),
                                'invoice_number': parts[1],
                                'company_name': parts[2],
                                'timestamp': parts[3] if len(parts) > 3 else ''
                            }
                            pdf_files.append(file_info)
                            print(f"解析结果: 发票号={file_info['invoice_number']}, 抬头={file_info['company_name']}")
                        else:
                            print(f"⚠️ 文件名格式不符合规范，跳过: {filename}")
                    except Exception as e:
                        print(f"❌ 解析文件名失败: {filename}, 错误: {str(e)}")

            print(f"找到 {len(pdf_files)} 个有效PDF文件")

            # 处理公司名称重复的文件
            pdf_files = self.handle_duplicate_company_files(folder_path, pdf_files)

            return pdf_files

        except Exception as e:
            print(f"❌ 扫描文件夹失败: {str(e)}")
            return pdf_files

    def handle_duplicate_company_files(self, folder_path, pdf_files):
        """处理公司名称重复的发票文件，将重复文件移动到专门文件夹"""
        try:
            import shutil
            import os

            if not pdf_files:
                return pdf_files

            print(f"=== 检查公司名称重复文件 ===")
            print(f"工作目录: {folder_path}")

            # 确保文件夹路径存在且可访问
            if not os.path.exists(folder_path):
                print(f"❌ 源文件夹不存在: {folder_path}")
                return pdf_files

            if not os.access(folder_path, os.R_OK | os.W_OK):
                print(f"❌ 源文件夹没有读写权限: {folder_path}")
                return pdf_files

            # 统计每个公司名称出现的次数
            company_count = {}
            for file_info in pdf_files:
                company_name = file_info.get('company_name', '')
                if company_name:
                    if company_name not in company_count:
                        company_count[company_name] = []
                    company_count[company_name].append(file_info)

            # 找出重复的公司名称
            duplicate_companies = {name: files for name, files in company_count.items() if len(files) > 1}

            if not duplicate_companies:
                print("✅ 未发现公司名称重复的文件")
                return pdf_files

            print(f"⚠️ 发现 {len(duplicate_companies)} 个公司名称有重复文件:")
            for company_name, files in duplicate_companies.items():
                print(f"   公司: {company_name} - {len(files)} 个文件")

            # 创建"公司名称重复"文件夹
            duplicate_folder = os.path.join(folder_path, "公司名称重复")
            try:
                if not os.path.exists(duplicate_folder):
                    os.makedirs(duplicate_folder, exist_ok=True)
                    print(f"✅ 创建文件夹: {duplicate_folder}")

                # 检查文件夹是否可写
                if not os.access(duplicate_folder, os.W_OK):
                    print(f"❌ 文件夹没有写入权限: {duplicate_folder}")
                    return pdf_files

            except PermissionError as e:
                print(f"❌ 创建文件夹权限不足: {duplicate_folder}, 错误: {str(e)}")
                return pdf_files
            except Exception as e:
                print(f"❌ 创建文件夹失败: {duplicate_folder}, 错误: {str(e)}")
                return pdf_files

            # 移动重复文件
            moved_files = []
            remaining_files = []

            for file_info in pdf_files:
                company_name = file_info.get('company_name', '')
                if company_name in duplicate_companies:
                    # 这是重复文件，需要移动
                    old_path = file_info['filepath']
                    new_path = os.path.join(duplicate_folder, file_info['filename'])

                    # 处理目标文件已存在的情况
                    if os.path.exists(new_path):
                        base_name, ext = os.path.splitext(file_info['filename'])
                        counter = 1
                        while os.path.exists(new_path):
                            new_filename = f"{base_name}_{counter}{ext}"
                            new_path = os.path.join(duplicate_folder, new_filename)
                            counter += 1
                        print(f"⚠️ 目标文件已存在，重命名为: {os.path.basename(new_path)}")

                    try:
                        # 确保源文件存在
                        if not os.path.exists(old_path):
                            print(f"⚠️ 源文件不存在，跳过: {file_info['filename']}")
                            remaining_files.append(file_info)
                            continue

                        shutil.move(old_path, new_path)
                        print(f"📁 移动文件: {file_info['filename']} → 公司名称重复/")
                        moved_files.append(file_info)
                    except PermissionError as e:
                        print(f"❌ 移动文件权限不足: {file_info['filename']}, 错误: {str(e)}")
                        remaining_files.append(file_info)
                    except Exception as e:
                        print(f"❌ 移动文件失败: {file_info['filename']}, 错误: {str(e)}")
                        # 移动失败的文件仍然保留在原列表中
                        remaining_files.append(file_info)
                else:
                    # 非重复文件，保留
                    remaining_files.append(file_info)

            print(f"✅ 处理完成: 移动了 {len(moved_files)} 个重复文件，保留 {len(remaining_files)} 个唯一文件")
            return remaining_files

        except Exception as e:
            print(f"❌ 处理重复文件失败: {str(e)}")
            # 出错时返回原始列表
            return pdf_files

    def get_page_orders(self, page):
        """获取页面中的所有订单信息（高性能优化版本）"""
        orders = []
        try:
            print("=== 获取页面订单信息 ===")
            print("正在扫描页面订单，请稍候...")

            # 先等待页面完全加载
            time.sleep(2)

            # 🚀 性能优化：先获取表格行总数，避免盲目递增查找
            base_tbody_xpath = '//*[@id="root"]/div/div/div/div[2]/div/div/div/div[5]/div[2]/div/div[2]/table/tbody'
            tbody_element = page.ele(f'xpath:{base_tbody_xpath}', timeout=3)

            if not tbody_element:
                print("❌ 未找到表格tbody元素，可能页面结构发生变化")
                return orders

            # 获取所有tr元素
            tr_elements = tbody_element.eles('tag:tr', timeout=2)
            total_rows = len(tr_elements)

            if total_rows == 0:
                print("⚠️ 表格中没有数据行")
                return orders

            print(f"📊 发现表格共有 {total_rows} 行数据，开始批量提取...")

            # 🎯 批量提取：基于实际行数进行精确提取
            base_order_xpath = f'{base_tbody_xpath}/tr'
            base_button_xpath = '//*[@id="root"]/div/div/div/div[2]/div/div/div/div[5]/div[2]/div/div[4]/div/div[2]/table/tbody/tr'

            for row_index in range(1, total_rows + 1):
                try:
                    # 构建当前行的XPath（基于发现的规律）
                    order_xpath = f'{base_order_xpath}[{row_index}]/td[2]/div/div[1]'
                    company_xpath = f'{base_order_xpath}[{row_index}]/td[6]/div'

                    # 快速获取元素（减少超时时间）
                    order_elem = page.ele(f'xpath:{order_xpath}', timeout=1)
                    company_elem = page.ele(f'xpath:{company_xpath}', timeout=1)

                    if order_elem and company_elem:
                        order_number = order_elem.text.strip() if order_elem.text else ""
                        company_name = company_elem.text.strip() if company_elem.text else ""

                        if order_number and company_name:
                            order_info = {
                                'row_index': row_index,
                                'order_number': order_number,
                                'company_name': company_name
                            }
                            orders.append(order_info)
                            print(f"✅ 第{row_index}/{total_rows}行: {order_number} → {company_name}")
                        else:
                            print(f"⚠️ 第{row_index}行数据为空，跳过")
                    else:
                        print(f"⚠️ 第{row_index}行元素获取失败，跳过")

                    # 进度提示（每10行显示一次）
                    if row_index % 10 == 0:
                        progress = (row_index / total_rows) * 100
                        print(f"📈 进度: {row_index}/{total_rows} ({progress:.1f}%) - 已获取{len(orders)}个有效订单")

                except Exception as e:
                    print(f"❌ 处理第{row_index}行时出错: {str(e)}")
                    continue

            print(f"🎉 扫描完成！总共{total_rows}行，成功获取{len(orders)}个有效订单")

            # 如果没有获取到任何订单，提供调试信息
            if len(orders) == 0:
                print("⚠️ 未获取到任何有效订单，进行调试分析...")
                print("正在检查表格结构...")

                # 检查第一行的结构
                if total_rows > 0:
                    first_row = tr_elements[0]
                    tds = first_row.eles('tag:td')
                    print(f"第一行有 {len(tds)} 个td元素")
                    for i, td in enumerate(tds):  # 显示所有td元素
                        text = td.text.strip() if td.text else ""
                        print(f"  td[{i+1}]: {text[:100]}...")  # 显示前100个字符

            return orders

        except Exception as e:
            print(f"❌ 获取页面订单信息失败: {str(e)}")
            return orders

    def find_matching_pdf(self, company_name, pdf_files):
        """查找匹配的PDF文件（智能匹配版本）"""
        if not company_name or not pdf_files:
            return None

        # 清理页面公司名称
        clean_page_name = self.clean_company_name(company_name)
        print(f"🔍 查找匹配: 页面公司名='{company_name}' → 清理后='{clean_page_name}'")

        # 1. 首先尝试精确匹配
        for file_info in pdf_files:
            file_company = file_info.get('company_name', '')
            clean_file_name = self.clean_company_name(file_company)

            if clean_page_name == clean_file_name:
                print(f"✅ 精确匹配成功: {file_info['filename']}")
                return file_info

        # 2. 尝试包含匹配（页面名称包含文件名称）
        for file_info in pdf_files:
            file_company = file_info.get('company_name', '')
            clean_file_name = self.clean_company_name(file_company)

            if clean_file_name and clean_file_name in clean_page_name:
                print(f"✅ 包含匹配成功: 页面'{clean_page_name}' 包含 文件'{clean_file_name}'")
                return file_info

        # 3. 尝试包含匹配（文件名称包含页面名称）
        for file_info in pdf_files:
            file_company = file_info.get('company_name', '')
            clean_file_name = self.clean_company_name(file_company)

            if clean_page_name and clean_page_name in clean_file_name:
                print(f"✅ 反向包含匹配成功: 文件'{clean_file_name}' 包含 页面'{clean_page_name}'")
                return file_info

        # 4. 输出调试信息
        print(f"❌ 未找到匹配的PDF文件")
        print(f"   页面公司名: '{company_name}' → '{clean_page_name}'")
        print(f"   可用PDF文件:")
        for i, file_info in enumerate(pdf_files[:5]):  # 只显示前5个
            file_company = file_info.get('company_name', '')
            clean_file_name = self.clean_company_name(file_company)
            print(f"     {i+1}. '{file_company}' → '{clean_file_name}'")
        if len(pdf_files) > 5:
            print(f"     ... 还有 {len(pdf_files) - 5} 个文件")

        return None

    def clean_company_name(self, name):
        """清理公司名称，用于智能匹配"""
        if not name:
            return ""

        # 转换为字符串并去除首尾空格
        clean_name = str(name).strip()

        # 去除常见的多余字符
        import re

        # 去除所有空白字符（包括全角空格）
        clean_name = re.sub(r'\s+', '', clean_name)

        # 去除常见的标点符号和括号内容
        clean_name = re.sub(r'[（）()【】\[\]《》<>""''\"\']+', '', clean_name)

        # 统一公司类型后缀
        company_suffixes = [
            ('有限责任公司', '有限公司'),
            ('股份有限公司', '股份公司'),
            ('集团有限公司', '集团'),
            ('(集团)有限公司', '集团'),
        ]

        for old_suffix, new_suffix in company_suffixes:
            if clean_name.endswith(old_suffix):
                clean_name = clean_name[:-len(old_suffix)] + new_suffix
                break

        return clean_name

    def extract_order_numbers_from_filename(self, filename):
        """从PDF文件名中提取订单编号"""
        if not filename:
            return []

        import re

        # PDF文件名格式: dzfp_编号_公司名_时间.pdf
        # 提取第一个下划线后的数字序列作为订单编号
        match = re.search(r'dzfp_(\d+)_', filename)
        if match:
            return [match.group(1)]

        # 如果没有找到标准格式，提取所有长数字序列
        numbers = re.findall(r'\d{10,}', filename)
        return list(set(numbers))

    def clean_order_number(self, order_number):
        """清理订单编号，去除前缀和特殊字符"""
        if not order_number:
            return ""

        import re

        # 转换为字符串
        clean_number = str(order_number).strip()

        # 去除常见前缀
        prefixes_to_remove = ['编号：', '订单号：', '订单编号：', '编号:', '订单号:', '订单编号:']
        for prefix in prefixes_to_remove:
            if clean_number.startswith(prefix):
                clean_number = clean_number[len(prefix):].strip()
                break

        # 只保留数字
        clean_number = re.sub(r'[^\d]', '', clean_number)

        return clean_number

    def move_uploaded_file(self, file_path):
        """将成功上传的文件移动到"已上传"目录"""
        try:
            import os
            import shutil

            if not os.path.exists(file_path):
                print(f"⚠️ 文件不存在，无法移动: {file_path}")
                return False

            # 获取项目根目录
            if getattr(sys, 'frozen', False):
                # 打包环境：获取exe文件所在目录
                project_root = os.path.dirname(sys.executable)
            else:
                # 开发环境：获取main.py所在目录
                project_root = os.path.dirname(os.path.abspath(__file__))

            # 创建"已上传"目录
            uploaded_dir = os.path.join(project_root, "已上传")
            if not os.path.exists(uploaded_dir):
                os.makedirs(uploaded_dir)
                print(f"✅ 创建已上传目录: {uploaded_dir}")

            # 获取文件名
            filename = os.path.basename(file_path)
            target_path = os.path.join(uploaded_dir, filename)

            # 如果目标文件已存在，添加时间戳后缀
            if os.path.exists(target_path):
                import time
                timestamp = int(time.time())
                name, ext = os.path.splitext(filename)
                filename = f"{name}_{timestamp}{ext}"
                target_path = os.path.join(uploaded_dir, filename)

            # 移动文件
            shutil.move(file_path, target_path)
            print(f"✅ 文件已移动到已上传目录: {filename}")
            return True

        except Exception as e:
            print(f"❌ 移动文件失败: {str(e)}")
            return False

    def find_invoice_entry_button_xpath(self, page, base_button_xpath, row_index):
        """查找指定行的"录入发票"按钮XPath"""
        try:
            # 构建当前行的基础路径
            current_row_xpath = f'{base_button_xpath}[{row_index}]'

            print(f"   🔍 查找第{row_index}行的录入发票按钮...")

            # 直接在当前行中查找包含"录入发票"文本的任何元素
            try:
                # 方法1: 直接查找包含"录入发票"文本的元素
                invoice_element = page.ele(f'text:录入发票', timeout=0.5)
                if invoice_element:
                    # 检查这个元素是否在当前行内
                    element_xpath = invoice_element.xpath
                    if element_xpath and f'[{row_index}]' in element_xpath:
                        print(f"   ✅ 通过文本直接找到录入发票元素")
                        return element_xpath

                # 方法2: 在当前行范围内查找包含"录入发票"文本的元素
                row_element = page.ele(f'xpath:{current_row_xpath}', timeout=1)
                if row_element:
                    # 在当前行中查找包含"录入发票"文本的任何元素
                    invoice_elements = row_element.eles('text:录入发票')
                    if invoice_elements:
                        print(f"   ✅ 在第{row_index}行中找到录入发票元素")
                        return invoice_elements[0].xpath

                    # 如果没找到"录入发票"，查找"录入"
                    invoice_elements = row_element.eles('text:录入')
                    if invoice_elements:
                        print(f"   ✅ 在第{row_index}行中找到录入元素")
                        return invoice_elements[0].xpath

            except Exception as e:
                print(f"   ⚠️ 文本查找失败: {str(e)}")

            # 方法3: 如果文本查找失败，尝试XPath查找
            print(f"   🔄 尝试XPath查找...")
            try:
                # 在当前行中查找包含"录入发票"文本的任何元素
                invoice_element = page.ele(f'xpath:{current_row_xpath}//*[contains(text(),"录入发票")]', timeout=0.5)
                if invoice_element:
                    print(f"   ✅ 通过XPath找到录入发票元素")
                    return invoice_element.xpath

                # 如果没找到"录入发票"，查找"录入"
                invoice_element = page.ele(f'xpath:{current_row_xpath}//*[contains(text(),"录入")]', timeout=0.5)
                if invoice_element:
                    print(f"   ✅ 通过XPath找到录入元素")
                    return invoice_element.xpath

            except Exception as e:
                print(f"   ⚠️ XPath查找失败: {str(e)}")

            # 如果都没找到，返回默认路径
            print(f"   ⚠️ 第{row_index}行未找到录入发票按钮，使用默认路径")
            return f'{current_row_xpath}//*[contains(text(),"录入")]'

        except Exception as e:
            print(f"   ❌ 查找第{row_index}行按钮时出错: {str(e)}")
            # 返回默认路径
            return f'{base_button_xpath}[{row_index}]//*[contains(text(),"录入")]'

    def find_matching_order(self, task_info, page_orders):
        """查找匹配的页面订单（严格按订单编号匹配）"""
        if not task_info or not page_orders:
            return None

        filename = task_info.get('filename', '')

        print(f"🔍 查找匹配订单:")
        print(f"   PDF文件: {filename}")

        # 从PDF文件名中提取订单编号
        file_numbers = self.extract_order_numbers_from_filename(filename)
        print(f"   文件名中的编号: {file_numbers}")

        # 使用订单编号匹配
        for order in page_orders:
            order_number = order.get('order_number', '')
            clean_order_number = self.clean_order_number(order_number)

            print(f"   检查页面订单: '{order_number}' → '{clean_order_number}'")

            for file_number in file_numbers:
                # 精确匹配
                if clean_order_number == file_number:
                    print(f"✅ 订单编号精确匹配: {clean_order_number}")
                    return order

                # 包含匹配
                if clean_order_number and file_number in clean_order_number:
                    print(f"✅ 页面订单号包含文件编号: '{clean_order_number}' 包含 '{file_number}'")
                    return order

                if file_number and clean_order_number in file_number:
                    print(f"✅ 文件编号包含页面订单号: '{file_number}' 包含 '{clean_order_number}'")
                    return order

        # 输出调试信息
        print(f"❌ 未找到匹配的订单")
        print(f"   文件编号: {file_numbers}")
        print(f"   可用页面订单:")
        for i, order in enumerate(page_orders):
            order_number = order.get('order_number', '')
            clean_order_number = self.clean_order_number(order_number)
            company_name = order.get('company_name', '')
            print(f"     {i+1}. '{order_number}' → '{clean_order_number}' ({company_name})")

        return None

    def test_batch_upload_invoices(self):
        """测试模式的批量上传发票"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

            if not self.cookie:
                return {"status": "error", "message": "Cookie未设置，请先设置Cookie"}

            # 选择文件夹
            import tkinter as tk
            from tkinter import filedialog

            print("请选择包含发票PDF文件的文件夹...")
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            folder_path = filedialog.askdirectory(title="选择包含发票PDF文件的文件夹")
            root.destroy()

            if not folder_path:
                return {"status": "error", "message": "未选择文件夹"}

            print(f"选择的文件夹: {folder_path}")

            # 1. 扫描文件夹，解析PDF文件
            pdf_files = self.scan_and_parse_pdfs(folder_path)
            if not pdf_files:
                return {"status": "error", "message": "文件夹中没有找到有效的PDF文件"}

            # 2. 初始化浏览器并打开页面
            print("=== 初始化浏览器 ===")
            init_result = self.browser_manager.init_browser(self.cookie)
            if init_result["status"] != "success":
                return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面初始化失败"}

            # 跳转到发票申请页面
            test_url = "https://tgc.tmall.com/ds/page/supplier/application-invoice?tabKey=INVOICE_CONSUMER"
            print(f"跳转到发票申请页面...")
            page.get(test_url)
            time.sleep(3)

            current_url = page.url
            # 更准确的页面加载判断：检查是否真的到达了目标页面
            if not current_url.startswith("https://tgc.tmall.com/ds/page/supplier/application-invoice"):
                # 检查是否被重定向到登录页面
                if "basic_login" in current_url:
                    return {"status": "error", "message": f"需要重新登录，当前被重定向到登录页面: {current_url}"}
                else:
                    return {"status": "error", "message": f"页面跳转失败或Cookie无效，当前URL: {current_url}"}

            # 3. 获取页面中的所有订单
            orders = self.get_page_orders(page)
            if not orders:
                return {"status": "error", "message": "页面中没有找到订单信息"}

            # 4. 逐个处理每个订单
            success_count = 0
            skip_count = 0

            for i, order in enumerate(orders, 1):
                print(f"\n=== 处理第 {i} 个订单 ===")
                print(f"订单编号: {order['order_number']}")
                print(f"发票抬头: {order['company_name']}")

                # 查找匹配的PDF文件
                matched_file = self.find_matching_pdf(order['company_name'], pdf_files)

                if matched_file:
                    print(f"✅ 找到匹配文件: {matched_file['filename']}")

                    # 处理这个订单
                    result = self.process_single_order(page, order, matched_file)
                    if result:
                        success_count += 1
                        print(f"✅ 第 {i} 个订单测试完成")
                    else:
                        print(f"❌ 第 {i} 个订单测试失败")
                else:
                    print(f"⚠️ 第 {i} 个订单：未找到匹配的PDF文件")
                    print("跳过此订单")
                    skip_count += 1

                # 每个订单之间稍作停顿
                time.sleep(1)

            print(f"\n=== 测试完成 ===")
            print(f"总共处理了 {len(orders)} 个订单")
            print(f"成功测试: {success_count} 个")
            print(f"跳过订单: {skip_count} 个")

            return {
                "status": "success",
                "message": f"测试完成！总共{len(orders)}个订单，成功测试{success_count}个，跳过{skip_count}个",
                "total": len(orders),
                "success": success_count,
                "skipped": skip_count
            }

        except Exception as e:
            print(f"❌ 测试批量上传失败: {str(e)}")
            return {"status": "error", "message": f"测试批量上传失败: {str(e)}"}

    def process_single_order(self, page, order, matched_file):
        """处理单个订单的完整流程"""
        try:
            # 1. 点击录入按钮
            print("📝 点击录入发票按钮...")

            # 直接使用文本查找录入发票按钮
            print("🔍 查找录入发票按钮...")
            buttons = page.eles('text:录入')
            button = None

            if buttons:
                # 使用第一个找到的录入按钮
                button = buttons[0]
                button_text = button.text.strip() if button.text else ""
                print(f"✅ 通过文本找到录入按钮: '{button_text}'")

            if not button:
                print("❌ 录入按钮未找到")
                return False

            # 最终确认按钮文本
            final_button_text = button.text.strip() if button.text else ""
            print(f"✅ 即将点击按钮: '{final_button_text}'")

            # 增强的点击策略
            try:
                # 1. 先滚动到元素位置
                print("📍 滚动到按钮位置...")
                button.scroll.to_see()
                time.sleep(0.5)

                # 2. 确保元素可见
                if not button.states.is_displayed:
                    print("⚠️ 按钮不可见，尝试其他方法...")

                # 3. 尝试多种点击方式
                print("🖱️ 尝试普通点击...")
                button.click()
                time.sleep(1)

                # 检查弹窗是否出现
                modal_xpath = '/html/body/div[4]/div/div'
                modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                if modal:
                    print("✅ 普通点击成功，弹窗已出现")
                else:
                    print("⚠️ 普通点击可能无效，尝试JavaScript点击...")
                    # 4. 使用安全的JavaScript点击方法
                    self.browser_manager.safe_js_click(button, page)
                    time.sleep(1)

                    modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                    if modal:
                        print("✅ JavaScript点击成功，弹窗已出现")
                    else:
                        print("⚠️ JavaScript点击也无效，尝试点击父元素...")
                        # 5. 尝试点击父元素
                        parent = button.parent()
                        if parent:
                            parent.click()
                            time.sleep(1)
                            modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                            if modal:
                                print("✅ 点击父元素成功，弹窗已出现")
                            else:
                                print("❌ 所有点击方式都失败")

                print("录入按钮点击结果: 成功")

            except Exception as e:
                print(f"⚠️ 点击过程中出错: {str(e)}")
                # 备用点击方式
                try:
                    button.click()
                    print("录入按钮点击结果: 备用方式成功")
                except Exception as e2:
                    print(f"❌ 备用点击也失败: {str(e2)}")
                    return False

            # 2. 等待弹窗出现
            print("⏳ 等待录入弹窗出现...")
            time.sleep(2)

            modal_xpath = '/html/body/div[4]/div/div'
            modal = page.ele(f'xpath:{modal_xpath}')
            if not modal:
                print("❌ 弹窗未出现")
                return False

            print("弹窗出现状态: 成功")

            # 2.5. 点击"全电发票（普通发票）"选项
            print("🔘 查找并点击全电发票（普通发票）选项...")
            try:
                # 使用文本方式定位"全电发票（普通发票）"选项
                invoice_type_element = modal.ele('text:全电发票（普通发票）')
                if invoice_type_element:
                    print("✅ 找到全电发票（普通发票）选项")
                    invoice_type_element.click()
                    print("✅ 全电发票（普通发票）选项点击成功")
                    time.sleep(1)  # 等待选项生效
                else:
                    # 如果找不到完整文本，尝试查找包含"全电发票"的元素
                    print("⚠️ 未找到完整文本，尝试查找包含'全电发票'的元素...")
                    invoice_elements = modal.eles('text:全电发票')
                    if invoice_elements:
                        # 选择第一个包含"全电发票"的元素
                        invoice_type_element = invoice_elements[0]
                        print(f"✅ 找到全电发票选项: {invoice_type_element.text}")
                        invoice_type_element.click()
                        print("✅ 全电发票选项点击成功")
                        time.sleep(1)
                    else:
                        print("⚠️ 未找到全电发票选项，继续执行后续流程...")
            except Exception as e:
                print(f"⚠️ 点击全电发票选项时出错: {str(e)}，继续执行后续流程...")

            # 3. 上传PDF文件
            print(f"📤 上传PDF文件: {matched_file['filename']}")

            # 查找上传按钮
            upload_elements = modal.eles('text:上传PDF')
            upload_button = None
            for elem in upload_elements:
                if elem.tag in ['button', 'label'] or 'btn' in (elem.attr('class') or ''):
                    upload_button = elem
                    break

            if not upload_button:
                print("❌ 上传PDF按钮未找到")
                return False

            # 点击上传按钮
            upload_button.click()
            time.sleep(1)

            # 查找文件输入框并上传文件
            file_input = modal.ele('tag:input@type=file')
            if not file_input:
                print("❌ 文件输入框未找到")
                return False

            file_input.input(matched_file['filepath'])
            print("✅ 文件上传到输入框成功")

            # 4. 等待页面处理文件
            print("⏳ 等待页面处理上传的文件...")
            time.sleep(3)

            # 5. 验证自动填入的信息（可选）
            print("🔍 验证自动填入信息...")
            self.verify_auto_filled_info(modal, matched_file)

            # 6. 查找并点击完成按钮
            print("🔍 查找完成按钮...")

            # 只使用文本查找方法
            complete_button = page.ele('text:完成')

            if not complete_button:
                print("❌ 完成按钮未找到")
                return False

            print("✅ 点击完成按钮...")
            # 使用窗口状态检查方法
            result = self.click_complete_and_verify_closure(page, complete_button, modal_xpath)
            if result:
                print("✅ 测试完成 - 文件已上传并点击完成按钮")
                # 上传成功后移动文件到"已上传"目录
                self.move_uploaded_file(matched_file['filepath'])
            else:
                print("⚠️ 测试完成但窗口关闭可能有问题")
            return result

        except Exception as e:
            print(f"❌ 处理订单失败: {str(e)}")
            return False

    def verify_auto_filled_info(self, modal, matched_file):
        """验证自动填入的信息"""
        try:
            # 查找发票号码输入框
            invoice_no_input = modal.ele('tag:input@name=invoiceNo')
            if invoice_no_input:
                filled_invoice_no = invoice_no_input.value or ""
                expected_invoice_no = matched_file['invoice_number']
                if filled_invoice_no == expected_invoice_no:
                    print(f"发票号码字段: {filled_invoice_no} ✅")
                else:
                    print(f"发票号码字段: 期望={expected_invoice_no}, 实际={filled_invoice_no} ❌")
            else:
                print("发票号码输入框未找到")

            # 查找发票抬头输入框
            invoice_title_input = modal.ele('tag:input@name=invoiceTitle')
            if invoice_title_input:
                filled_title = invoice_title_input.value or ""
                expected_title = matched_file['company_name']
                if filled_title == expected_title:
                    print(f"发票抬头字段: {filled_title} ✅")
                else:
                    print(f"发票抬头字段: 期望={expected_title}, 实际={filled_title} ❌")
            else:
                print("发票抬头输入框未找到")

        except Exception as e:
            print(f"验证自动填入信息失败: {str(e)}")

    def select_invoice_folder(self):
        """选择发票文件夹并扫描PDF文件"""
        try:
            import tkinter as tk
            from tkinter import filedialog

            print("请选择包含发票PDF文件的文件夹...")
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            folder_path = filedialog.askdirectory(title="选择包含发票PDF文件的文件夹")
            root.destroy()

            if not folder_path:
                return {"status": "error", "message": "未选择文件夹"}

            print(f"选择的文件夹: {folder_path}")

            # 扫描文件夹，解析PDF文件
            pdf_files = self.scan_and_parse_pdfs(folder_path)
            if not pdf_files:
                return {"status": "error", "message": "文件夹中没有找到有效的PDF文件"}

            return {
                "status": "success",
                "message": f"成功扫描到 {len(pdf_files)} 个PDF文件",
                "folder_path": folder_path,
                "pdf_files": pdf_files
            }

        except Exception as e:
            print(f"选择文件夹失败: {str(e)}")
            return {"status": "error", "message": f"选择文件夹失败: {str(e)}"}

    def init_upload_browser(self):
        """初始化上传浏览器（优化版本）"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

            if not self.cookie:
                return {"status": "error", "message": "Cookie未设置，请先设置Cookie"}

            print("=== 初始化上传浏览器 ===")
            print("正在启动浏览器...")

            # 初始化浏览器
            init_result = self.browser_manager.init_browser(self.cookie)
            if init_result["status"] != "success":
                return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面初始化失败"}

            print("浏览器启动成功，正在跳转到发票申请页面...")

            # 跳转到发票申请页面
            test_url = "https://tgc.tmall.com/ds/page/supplier/application-invoice?tabKey=INVOICE_CONSUMER"
            page.get(test_url)

            print("页面跳转完成，等待页面加载...")
            time.sleep(3)

            current_url = page.url
            print(f"当前页面URL: {current_url}")

            # 更准确的页面加载判断：检查是否真的到达了目标页面
            if not current_url.startswith("https://tgc.tmall.com/ds/page/supplier/application-invoice"):
                # 检查是否被重定向到登录页面
                if "basic_login" in current_url:
                    return {"status": "error", "message": f"需要重新登录，当前被重定向到登录页面: {current_url}"}
                else:
                    return {"status": "error", "message": f"页面跳转失败或Cookie无效，当前URL: {current_url}"}

            print("页面加载成功，尝试点击发票申请倒计时元素...")

            # 尝试查找并点击"发票申请倒计时"元素
            try:
                # 使用文本查找方式
                countdown_element = page.ele('text:发票申请倒计时')
                if countdown_element:
                    print("✅ 找到发票申请倒计时元素，正在点击...")
                    countdown_element.click()
                    print("✅ 发票申请倒计时元素点击成功")
                    time.sleep(1)  # 等待点击后的页面响应
                else:
                    print("⚠️ 未找到发票申请倒计时元素，继续执行后续流程...")
            except Exception as e:
                print(f"⚠️ 点击发票申请倒计时元素时出错: {str(e)}，继续执行后续流程...")

            print("开始获取订单信息...")

            # 获取页面中的所有订单，保存到实例变量中
            self.page_orders = self.get_page_orders(page)

            if not self.page_orders:
                return {"status": "error", "message": "页面中没有找到订单信息，可能页面结构发生变化"}

            print(f"✅ 浏览器初始化完成！找到 {len(self.page_orders)} 个订单")

            # 输出前几个订单作为确认
            print("前3个订单预览:")
            for i, order in enumerate(self.page_orders[:3]):
                print(f"  {i+1}. {order['company_name']} (订单号: {order['order_number']})")

            return {
                "status": "success",
                "message": f"浏览器初始化成功，找到 {len(self.page_orders)} 个订单，可以开始上传"
            }

        except Exception as e:
            print(f"❌ 初始化浏览器失败: {str(e)}")
            return {"status": "error", "message": f"初始化浏览器失败: {str(e)}"}

    def log_performance(self, operation, start_time):
        """记录操作性能"""
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏱️ {operation} 耗时: {duration:.2f}秒")
        return end_time

    def process_single_invoice_upload(self, task):
        """处理单个发票上传任务（优化版本）"""
        start_time = time.time()
        try:
            print(f"\n=== 处理发票上传任务 ===")
            print(f"文件名: {task['filename']}")
            print(f"发票抬头: {task['company_name']}")
            print(f"发票号码: {task['invoice_number']}")

            # 在页面订单中查找匹配的订单（使用智能匹配）
            match_start = time.time()
            matched_order = self.find_matching_order(task, self.page_orders)
            self.log_performance("订单匹配", match_start)

            if not matched_order:
                print(f"❌ 未找到匹配的订单")
                print(f"任务公司名: '{task['company_name']}'")
                print(f"可用订单列表:")
                for i, order in enumerate(self.page_orders[:5]):  # 只显示前5个
                    print(f"  {i+1}. '{order['company_name']}'")
                if len(self.page_orders) > 5:
                    print(f"  ... 还有 {len(self.page_orders) - 5} 个订单")

                return {
                    "status": "skipped",
                    "message": f"未找到匹配的订单: {task['company_name']}"
                }

            print(f"✅ 找到匹配订单: {matched_order['order_number']}")

            # 获取浏览器页面
            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面未初始化"}

            # 处理这个订单
            upload_start = time.time()
            result = self.process_single_order_upload(page, matched_order, task)
            self.log_performance("订单上传处理", upload_start)

            if result:
                self.log_performance("整个任务处理", start_time)
                print(f"✅ 发票上传成功")
                return {
                    "status": "success",
                    "message": "上传成功"
                }
            else:
                print(f"❌ 发票上传失败")
                return {
                    "status": "error",
                    "message": "上传失败"
                }

        except Exception as e:
            print(f"❌ 处理发票上传任务失败: {str(e)}")
            return {"status": "error", "message": f"处理失败: {str(e)}"}

    def process_orders_with_invoices(self, pdf_files):
        """以订单为主导处理发票上传（简化版本）"""
        try:
            print(f"\n=== 开始以订单为主导的上传流程 ===")
            print(f"发票文件数量: {len(pdf_files)}")

            total_success_count = 0
            total_skip_count = 0
            total_error_count = 0
            current_page = 1
            max_pages = 50  # 设置最大页数限制

            # 获取浏览器页面
            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面未初始化"}

            while current_page <= max_pages:
                print(f"\n📄 === 处理第 {current_page} 页 ===")

                # 获取当前页面的订单（使用现有的page_orders）
                if current_page == 1:
                    # 第一页使用已经获取的订单
                    current_page_orders = self.page_orders
                else:
                    # 其他页面重新获取订单
                    current_page_orders = self.get_current_page_orders(page)

                if not current_page_orders:
                    print(f"第 {current_page} 页没有订单数据，结束处理")
                    break

                print(f"第 {current_page} 页订单数量: {len(current_page_orders)}")

                # 处理当前页面的每个订单
                page_success = 0
                page_skip = 0
                page_error = 0

                for i, order in enumerate(current_page_orders, 1):
                    print(f"\n--- 第{current_page}页第{i}/{len(current_page_orders)}个订单 ---")
                    print(f"订单编号: {order['order_number']}")
                    print(f"发票抬头: {order['company_name']}")

                    # 根据公司名称查找匹配的发票文件
                    matched_invoice = self.find_matching_invoice_by_order(order, pdf_files)

                    if not matched_invoice:
                        print(f"⚠️ 订单 {order['order_number']} 未找到匹配的发票文件，跳过")
                        page_skip += 1
                        continue

                    print(f"✅ 找到匹配发票: {matched_invoice['filename']}")

                    # 处理这个订单的发票上传
                    result = self.process_single_order_upload(page, order, matched_invoice)

                    if result:
                        page_success += 1
                        print(f"✅ 订单 {order['order_number']} 上传成功")
                    else:
                        page_error += 1
                        print(f"❌ 订单 {order['order_number']} 上传失败")

                    # 任务间隔
                    time.sleep(1)

                # 累计统计
                total_success_count += page_success
                total_skip_count += page_skip
                total_error_count += page_error

                print(f"\n📊 第 {current_page} 页处理完成:")
                print(f"   成功: {page_success}, 跳过: {page_skip}, 失败: {page_error}")

                # 尝试翻到下一页
                if self.navigate_to_next_page_by_text(page):
                    current_page += 1
                    time.sleep(3)  # 等待页面加载
                    print(f"✅ 成功翻到第 {current_page} 页")
                else:
                    print("已到达最后一页或翻页失败，结束处理")
                    break

            print(f"\n🎉 多页批量上传完成！")
            print(f"总计 - 成功: {total_success_count}, 跳过: {total_skip_count}, 失败: {total_error_count}")
            print(f"共处理了 {current_page} 页")

            return {
                "status": "success",
                "message": f"多页上传完成！成功: {total_success_count}, 跳过: {total_skip_count}, 失败: {total_error_count}",
                "success_count": total_success_count,
                "skip_count": total_skip_count,
                "error_count": total_error_count,
                "pages_processed": current_page
            }

        except Exception as e:
            print(f"❌ 多页批量上传失败: {str(e)}")
            return {"status": "error", "message": f"多页批量上传失败: {str(e)}"}

    def get_current_page_orders(self, page):
        """获取当前页面的订单信息（简化版本）"""
        try:
            print("正在获取当前页面订单...")
            orders = []

            # 等待页面加载
            time.sleep(2)

            base_tbody_xpath = '//*[@id="root"]/div/div/div/div[2]/div/div/div/div[5]/div[2]/div/div[2]/table/tbody'
            tbody_element = page.ele(f'xpath:{base_tbody_xpath}', timeout=3)

            if not tbody_element:
                print("❌ 未找到表格tbody元素")
                return orders

            tr_elements = tbody_element.eles('tag:tr', timeout=2)
            total_rows = len(tr_elements)

            if total_rows == 0:
                print("⚠️ 表格中没有数据行")
                return orders

            print(f"📊 发现 {total_rows} 行数据")

            base_order_xpath = f'{base_tbody_xpath}/tr'

            for row_index in range(1, total_rows + 1):
                try:
                    order_xpath = f'{base_order_xpath}[{row_index}]/td[2]/div/div[1]'
                    company_xpath = f'{base_order_xpath}[{row_index}]/td[6]/div'

                    order_elem = page.ele(f'xpath:{order_xpath}', timeout=1)
                    company_elem = page.ele(f'xpath:{company_xpath}', timeout=1)

                    if order_elem and company_elem:
                        order_number = order_elem.text.strip() if order_elem.text else ""
                        company_name = company_elem.text.strip() if company_elem.text else ""

                        if order_number and company_name:
                            order_info = {
                                'row_index': row_index,
                                'order_number': order_number,
                                'company_name': company_name
                            }
                            orders.append(order_info)
                            print(f"✅ 第{row_index}行: {order_number} → {company_name}")

                except Exception as e:
                    print(f"❌ 处理第{row_index}行时出错: {str(e)}")
                    continue

            print(f"✅ 获取到 {len(orders)} 个订单")
            return orders

        except Exception as e:
            print(f"❌ 获取当前页面订单失败: {str(e)}")
            return []

    def navigate_to_next_page_by_text(self, page):
        """点击下一页按钮"""
        try:
            print("🔍 查找下一页按钮...")

            # 使用精确的选择器查找下一页按钮
            next_button = page.ele('css:button.next-btn.next-next', timeout=3)

            if next_button:
                button_text = next_button.text.strip() if next_button.text else ""
                aria_label = next_button.attr('aria-label') or ''

                print(f"🎯 找到下一页按钮: 文本:'{button_text}', aria-label:'{aria_label}'")

                # 检查是否禁用
                disabled_attr = next_button.attr('disabled')
                button_class = next_button.attr('class') or ''
                if disabled_attr or 'disabled' in button_class:
                    print("⚠️ 下一页按钮已禁用，已到最后一页")
                    return False

                # 点击下一页按钮
                print("✅ 正在点击下一页按钮...")
                next_button.click()
                print("✅ 下一页按钮点击成功")
                return True
            else:
                print("❌ 未找到下一页按钮，可能已到最后一页")
                return False

        except Exception as e:
            print(f"❌ 翻页失败: {str(e)}")
            return False

    def find_matching_invoice_by_order(self, order, pdf_files):
        """根据订单信息查找匹配的发票文件（通过公司名称匹配）"""
        try:
            order_number = order.get('order_number', '')
            company_name = order.get('company_name', '')

            print(f"🔍 查找订单匹配的发票:")
            print(f"   订单编号: {order_number}")
            print(f"   发票抬头: {company_name}")

            # 清理公司名称
            clean_company_name = self.clean_company_name(company_name)
            print(f"   清理后公司名称: {clean_company_name}")

            # 通过公司名称进行匹配
            for invoice in pdf_files:
                invoice_company = invoice.get('company_name', '')
                clean_invoice_company = self.clean_company_name(invoice_company)

                print(f"   检查发票文件: {invoice['filename']}")
                print(f"   发票公司名称: {invoice_company} → 清理后: {clean_invoice_company}")

                # 精确匹配
                if clean_company_name == clean_invoice_company:
                    print(f"✅ 公司名称精确匹配: {clean_company_name}")
                    return invoice

                # 包含匹配（页面公司名包含发票公司名）
                if clean_company_name and clean_invoice_company in clean_company_name:
                    print(f"✅ 公司名称包含匹配: '{clean_company_name}' 包含 '{clean_invoice_company}'")
                    return invoice

                # 包含匹配（发票公司名包含页面公司名）
                if clean_invoice_company and clean_company_name in clean_invoice_company:
                    print(f"✅ 公司名称包含匹配: '{clean_invoice_company}' 包含 '{clean_company_name}'")
                    return invoice

            print(f"❌ 未找到匹配的发票文件")
            return None

        except Exception as e:
            print(f"❌ 查找匹配发票时出错: {str(e)}")
            return None

    def process_single_order_upload(self, page, order, task):
        """处理单个订单的上传流程（正式版本）"""
        try:
            # 1. 点击录入按钮
            print("📝 点击录入发票按钮...")

            # 直接使用文本查找录入发票按钮
            print("🔍 查找录入发票按钮...")
            buttons = page.eles('text:录入')
            button = None

            if buttons:
                # 使用第一个找到的录入按钮
                button = buttons[0]
                button_text = button.text.strip() if button.text else ""
                print(f"✅ 通过文本找到录入按钮: '{button_text}'")

            if not button:
                print("❌ 录入按钮未找到")
                return False

            # 最终确认按钮文本
            final_button_text = button.text.strip() if button.text else ""
            print(f"✅ 即将点击按钮: '{final_button_text}'")

            # 增强的点击策略
            try:
                # 1. 先滚动到元素位置
                print("📍 滚动到按钮位置...")
                button.scroll.to_see()
                time.sleep(0.5)

                # 2. 确保元素可见
                if not button.states.is_displayed:
                    print("⚠️ 按钮不可见，尝试其他方法...")

                # 3. 尝试多种点击方式
                print("🖱️ 尝试普通点击...")
                button.click()
                time.sleep(1)

                # 检查弹窗是否出现
                modal_xpath = '/html/body/div[4]/div/div'
                modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                if modal:
                    print("✅ 普通点击成功，弹窗已出现")
                else:
                    print("⚠️ 普通点击可能无效，尝试JavaScript点击...")
                    # 4. 使用安全的JavaScript点击方法
                    self.browser_manager.safe_js_click(button, page)
                    time.sleep(1)

                    modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                    if modal:
                        print("✅ JavaScript点击成功，弹窗已出现")
                    else:
                        print("⚠️ JavaScript点击也无效，尝试点击父元素...")
                        # 5. 尝试点击父元素
                        parent = button.parent()
                        if parent:
                            parent.click()
                            time.sleep(1)
                            modal = page.ele(f'xpath:{modal_xpath}', timeout=1)
                            if modal:
                                print("✅ 点击父元素成功，弹窗已出现")
                            else:
                                print("❌ 所有点击方式都失败")

                print("录入按钮点击结果: 成功")

            except Exception as e:
                print(f"⚠️ 点击过程中出错: {str(e)}")
                # 备用点击方式
                try:
                    button.click()
                    print("录入按钮点击结果: 备用方式成功")
                except Exception as e2:
                    print(f"❌ 备用点击也失败: {str(e2)}")
                    return False

            # 2. 等待弹窗出现
            print("⏳ 等待录入弹窗出现...")
            time.sleep(2)

            modal_xpath = '/html/body/div[4]/div/div'
            modal = page.ele(f'xpath:{modal_xpath}')
            if not modal:
                print("❌ 弹窗未出现")
                return False

            print("弹窗出现状态: 成功")

            # 2.5. 点击"全电发票（普通发票）"选项
            print("🔘 查找并点击全电发票（普通发票）选项...")
            try:
                # 使用文本方式定位"全电发票（普通发票）"选项
                invoice_type_element = modal.ele('text:全电发票（普通发票）')
                if invoice_type_element:
                    print("✅ 找到全电发票（普通发票）选项")
                    invoice_type_element.click()
                    print("✅ 全电发票（普通发票）选项点击成功")
                    time.sleep(1)  # 等待选项生效
                else:
                    # 如果找不到完整文本，尝试查找包含"全电发票"的元素
                    print("⚠️ 未找到完整文本，尝试查找包含'全电发票'的元素...")
                    invoice_elements = modal.eles('text:全电发票')
                    if invoice_elements:
                        # 选择第一个包含"全电发票"的元素
                        invoice_type_element = invoice_elements[0]
                        print(f"✅ 找到全电发票选项: {invoice_type_element.text}")
                        invoice_type_element.click()
                        print("✅ 全电发票选项点击成功")
                        time.sleep(1)
                    else:
                        print("⚠️ 未找到全电发票选项，继续执行后续流程...")
            except Exception as e:
                print(f"⚠️ 点击全电发票选项时出错: {str(e)}，继续执行后续流程...")

            # 3. 上传PDF文件
            print(f"📤 上传PDF文件: {task['filename']}")

            # 方法一：直接操作文件输入框（避免系统文件选择框）
            print("正在查找文件输入框...")
            file_input = modal.ele('tag:input@type=file')

            if file_input:
                print("找到文件输入框，尝试直接设置文件路径")
                try:
                    # 方法一：直接设置文件路径，避免弹出系统文件选择框
                    file_input.input(task['filepath'])
                    print("✅ 方法一成功：文件路径设置成功")
                    time.sleep(2)  # 等待文件处理

                    # 验证文件是否设置成功
                    if file_input.value or file_input.attr('value'):
                        print("✅ 文件输入框已有值，文件上传成功")
                        # 文件上传成功，继续后续流程（不在这里返回）
                    else:
                        print("文件输入框仍为空，尝试其他方法")
                        raise Exception("文件输入框值未设置")

                except Exception as e:
                    print(f"❌ 方法一失败: {str(e)}")
                    print("尝试方法二：处理上传按钮...")

                    # 查找上传按钮
                    upload_elements = modal.eles('text:上传PDF')
                    upload_button = None
                    for elem in upload_elements:
                        if elem.tag in ['button', 'label'] or 'btn' in (elem.attr('class') or ''):
                            upload_button = elem
                            break

                    if upload_button:
                        # 尝试方法三：剪贴板方法
                        if self.upload_file_method_three(upload_button, task):
                            print("✅ 方法三成功：剪贴板方法")
                            # 继续执行后续流程，不在这里返回
                        elif self.handle_system_file_dialog(upload_button, task):
                            print("✅ 方法二成功：pyautogui方法")
                            # 继续执行后续流程，不在这里返回
                        else:
                            print("❌ 所有上传方法都失败了")
                            return False
                    else:
                        print("❌ 未找到上传按钮")
                        return False
            else:
                print("❌ 未找到文件输入框")
                return False

            # 4. 等待页面处理文件
            print("⏳ 等待页面处理上传的文件...")
            time.sleep(3)

            # 5. 验证自动填入的信息
            print("🔍 验证自动填入信息...")
            self.verify_auto_filled_info(modal, task)

            # 6. 查找取消和完成按钮
            print("🔍 查找操作按钮...")

            # 只使用文本查找方法
            cancel_button = page.ele('text:取消')
            complete_button = page.ele('text:完成')

            if cancel_button:
                print("✅ 找到取消按钮")
            else:
                print("❌ 未找到取消按钮")

            if complete_button:
                print("✅ 找到完成按钮")
            else:
                print("❌ 未找到完成按钮")

            # 7. 默认点击取消按钮
            # print("❌ 点击取消按钮...")
            # if cancel_button:
            #     cancel_button.click()
            #     print("✅ 取消按钮点击成功")
            #
            #     # 等待弹窗关闭
            #     time.sleep(1)
            #     print("✅ 任务处理完成 - 文件已上传并点击取消按钮")
            #     return True
            # else:
            #     print("❌ 取消按钮未找到")
            #     return False

            # 如果需要点击完成按钮，取消注释下面的代码
            print("✅ 点击完成按钮...")
            if complete_button:
                # 点击完成按钮并检查窗口状态
                result = self.click_complete_and_verify_closure(page, complete_button, modal_xpath)
                if result:
                    # 上传成功后移动文件到"已上传"目录
                    self.move_uploaded_file(task['filepath'])
                return result
            else:
                print("❌ 完成按钮未找到")
                return False

        except Exception as e:
            print(f"❌ 处理订单上传失败: {str(e)}")
            return False

    def click_complete_and_verify_closure(self, page, complete_button, modal_xpath):
        """点击完成按钮并刷新页面继续流程"""
        try:
            print("✅ 点击完成按钮...")
            complete_button.click()
            print("✅ 完成按钮点击成功")

            # 等待一段时间让页面处理
            time.sleep(2)

            # 直接刷新页面，不检查成功提示框
            print("🔄 刷新页面...")
            page.refresh()
            print("✅ 页面刷新完成")

            # 等待页面加载
            print("⏳ 等待页面加载...")
            time.sleep(3)

            # 点击发票申请倒计时元素
            print("🔍 查找并点击发票申请倒计时元素...")
            try:
                # 使用文本查找方式
                countdown_element = page.ele('text:发票申请倒计时')
                if countdown_element:
                    print("✅ 找到发票申请倒计时元素，正在点击...")
                    countdown_element.click()
                    print("✅ 发票申请倒计时元素点击成功")
                    time.sleep(1)  # 等待点击后的页面响应
                else:
                    print("⚠️ 未找到发票申请倒计时元素，继续执行后续流程...")
            except Exception as e:
                print(f"⚠️ 点击发票申请倒计时元素时出错: {str(e)}，继续执行后续流程...")

            print("✅ 完成按钮处理完成，准备继续订单和发票比对")
            return True

        except Exception as e:
            print(f"❌ 点击完成按钮过程中出错: {str(e)}")
            # 出错时也尝试关闭窗口
            try:
                close_button = page.ele('text:取消') or page.ele('text:关闭') or page.ele('text:×')
                if close_button:
                    close_button.click()
                    print("✅ 出错后已关闭弹窗")
            except:
                pass
            return False

    def upload_file_method_two(self, modal, task):
        """方法二：点击上传按钮后处理系统文件选择框"""
        try:
            print("使用方法二：点击上传按钮")

            # 查找上传按钮
            upload_elements = modal.eles('text:上传PDF')
            upload_button = None
            for elem in upload_elements:
                if elem.tag in ['button', 'label'] or 'btn' in (elem.attr('class') or ''):
                    upload_button = elem
                    break

            if not upload_button:
                print("❌ 上传PDF按钮未找到")
                return False

            print("找到上传按钮，准备处理文件选择框...")

            # 使用JavaScript直接设置文件输入框的值（避免弹出系统对话框）
            try:
                # 先尝试通过JavaScript设置文件
                js_code = f"""
                var fileInput = document.querySelector('input[type="file"]');
                if (fileInput) {{
                    var file = new File([''], '{task['filename']}', {{type: 'application/pdf'}});
                    var dataTransfer = new DataTransfer();
                    dataTransfer.items.add(file);
                    fileInput.files = dataTransfer.files;

                    // 触发change事件
                    var event = new Event('change', {{bubbles: true}});
                    fileInput.dispatchEvent(event);

                    return 'success';
                }} else {{
                    return 'no_input_found';
                }}
                """

                page = modal.page if hasattr(modal, 'page') else self.browser_manager.page
                result = page.run_js(js_code)

                if result == 'success':
                    print("通过JavaScript设置文件成功")
                    return True
                else:
                    print("JavaScript方法失败，尝试其他方法")

            except Exception as e:
                print(f"JavaScript方法失败: {str(e)}")

            # 如果JavaScript方法失败，尝试使用pyautogui处理系统对话框
            return self.handle_system_file_dialog(upload_button, task)

        except Exception as e:
            print(f"方法二失败: {str(e)}")
            return False

    def handle_system_file_dialog(self, upload_button, task):
        """处理系统文件选择对话框"""
        try:
            print("尝试使用pyautogui处理系统文件对话框...")

            # 检查是否安装了pyautogui
            try:
                import pyautogui
                import threading
            except ImportError:
                print("❌ pyautogui未安装，无法处理系统文件对话框")
                print("请安装: pip install pyautogui")
                return False

            # 设置文件路径
            file_path = task['filepath']
            print(f"准备选择文件: {file_path}")

            # 定义处理文件对话框的函数
            def handle_dialog():
                try:
                    time.sleep(2)  # 等待对话框出现
                    print("正在处理文件选择对话框...")

                    # 输入文件路径
                    pyautogui.hotkey('ctrl', 'l')  # 打开地址栏
                    time.sleep(0.5)
                    pyautogui.typewrite(file_path)  # 输入文件路径
                    time.sleep(0.5)
                    pyautogui.press('enter')  # 确认选择

                    print("文件路径已输入，等待确认...")
                    time.sleep(1)

                except Exception as e:
                    print(f"处理文件对话框失败: {str(e)}")

            # 在后台线程中处理文件对话框
            dialog_thread = threading.Thread(target=handle_dialog)
            dialog_thread.daemon = True
            dialog_thread.start()

            # 点击上传按钮（这会弹出系统文件选择框）
            upload_button.click()
            print("已点击上传按钮，等待文件对话框处理...")

            # 等待对话框处理完成
            dialog_thread.join(timeout=10)  # 最多等待10秒

            time.sleep(2)  # 等待文件上传处理
            print("文件对话框处理完成")
            return True

        except Exception as e:
            print(f"处理系统文件对话框失败: {str(e)}")
            return False

    def upload_file_method_three(self, upload_button, task):
        """方法三：使用剪贴板处理文件选择"""
        try:
            print("使用方法三：剪贴板方法")

            # 检查是否安装了pyperclip
            try:
                import pyperclip
                import pyautogui
                import threading
            except ImportError:
                print("❌ 需要安装: pip install pyperclip pyautogui")
                return False

            file_path = task['filepath']
            print(f"准备通过剪贴板选择文件: {file_path}")

            def handle_clipboard_dialog():
                try:
                    time.sleep(2)  # 等待对话框出现
                    print("使用剪贴板输入文件路径...")

                    # 将文件路径复制到剪贴板
                    pyperclip.copy(file_path)

                    # 使用Ctrl+L打开地址栏，然后粘贴路径
                    pyautogui.hotkey('ctrl', 'l')
                    time.sleep(0.5)
                    pyautogui.hotkey('ctrl', 'v')  # 粘贴文件路径
                    time.sleep(0.5)
                    pyautogui.press('enter')

                    print("文件路径已通过剪贴板输入")

                except Exception as e:
                    print(f"剪贴板方法失败: {str(e)}")

            # 启动后台处理线程
            clipboard_thread = threading.Thread(target=handle_clipboard_dialog)
            clipboard_thread.daemon = True
            clipboard_thread.start()

            # 点击上传按钮
            upload_button.click()
            print("已点击上传按钮，等待剪贴板处理...")

            # 等待处理完成
            clipboard_thread.join(timeout=10)
            time.sleep(2)

            return True

        except Exception as e:
            print(f"剪贴板方法失败: {str(e)}")
            return False



    def get_company_code(self, company_name):
        """获取公司识别码，使用DrissionPage打开风险鸟网站"""
        try:
            if not DRISSION_AVAILABLE:
                return {"status": "error", "message": "DrissionPage未安装，无法使用浏览器功能"}

            if not company_name or company_name.strip() == '':
                return {"status": "error", "message": "公司名称不能为空"}

            print(f"开始获取公司识别码: {company_name}")

            # URL编码公司名称
            import urllib.parse
            encoded_company_name = urllib.parse.quote(company_name.strip())
            target_url = f"https://riskbird.com/search/company?keyword={encoded_company_name}"

            print(f"目标URL: {target_url}")

            # 每次都重新初始化浏览器，确保稳定性
            print("启动浏览器...")
            return self._init_and_navigate_riskbird(target_url)

        except Exception as e:
            print(f"获取公司识别码失败: {str(e)}")
            return {"status": "error", "message": f"获取公司识别码失败: {str(e)}"}

    def _init_and_navigate_riskbird(self, target_url):
        """初始化浏览器并导航到风险鸟网站"""
        try:
            # 加载风险鸟Cookie
            riskbird_cookie = self._load_riskbird_cookie()

            # 强制重新初始化浏览器（不传Cookie，避免连接问题）
            init_result = self.browser_manager.init_browser("")
            if init_result["status"] != "success":
                return init_result

            page = self.browser_manager.page
            if not page:
                return {"status": "error", "message": "浏览器页面初始化失败"}

            # 直接跳转到目标URL（不设置Cookie，避免连接问题）
            print(f"跳转到: {target_url}")
            page.get(target_url)

            # 等待页面加载
            import time
            time.sleep(2)

            # 保存新的Cookie
            self._save_riskbird_cookies()

            return {
                "status": "success",
                "message": "浏览器已启动并跳转到风险鸟网站",
                "url": target_url
            }

        except Exception as e:
            print(f"初始化浏览器并导航失败: {str(e)}")
            # 确保清理状态
            try:
                if self.browser_manager.page:
                    self.browser_manager.page.quit()
            except:
                pass
            self.browser_manager.page = None
            self.browser_manager.is_initialized = False
            return {"status": "error", "message": f"初始化浏览器失败: {str(e)}"}

    def _load_riskbird_cookie(self):
        """加载风险鸟Cookie"""
        try:
            config_file = get_config_path()
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    riskbird_cookie = config.get('riskbird_cookie', '')
                    if riskbird_cookie:
                        print("已加载风险鸟Cookie")
                        return riskbird_cookie
                    else:
                        print("风险鸟Cookie为空")
                        return ""
            else:
                print("配置文件不存在")
                return ""
        except Exception as e:
            print(f"加载风险鸟Cookie失败: {str(e)}")
            return ""

    def _save_riskbird_cookies(self):
        """保存风险鸟网站的Cookie"""
        try:
            if not self.browser_manager.is_initialized or not self.browser_manager.page:
                print("浏览器未初始化，无法保存Cookie")
                return False

            page = self.browser_manager.page

            # 获取当前页面的所有Cookie
            all_cookies = page.cookies()
            riskbird_cookies = []

            # 筛选风险鸟域名的Cookie
            for cookie in all_cookies:
                domain = cookie.get('domain', '')
                if 'riskbird.com' in domain:
                    name = cookie.get('name', '')
                    value = cookie.get('value', '')
                    if name and value:
                        riskbird_cookies.append(f"{name}={value}")

            if riskbird_cookies:
                cookie_string = '; '.join(riskbird_cookies)
                print(f"获取到 {len(riskbird_cookies)} 个风险鸟Cookie")

                # 保存到配置文件
                config_file = get_config_path()
                config = {}

                # 读取现有配置
                if os.path.exists(config_file):
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                # 更新风险鸟Cookie
                config['riskbird_cookie'] = cookie_string

                # 保存配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                print("风险鸟Cookie已保存到配置文件")
                return True
            else:
                print("未找到风险鸟相关Cookie")
                return False

        except Exception as e:
            print(f"保存风险鸟Cookie失败: {str(e)}")
            return False


# 创建API实例
api = InvoiceAPI()

def create_app():
    """创建webview应用"""
    # 启动HTTP服务器（在后台线程中）
    http_thread = threading.Thread(target=start_http_server, daemon=True)
    http_thread.start()

    # 等待HTTP服务器启动
    time.sleep(1)

    # 使用HTTP服务器提供HTML页面
    html_url = "http://localhost:8765/index.html"
    print(f"HTML页面URL: {html_url}")

    window = webview.create_window(
        '发票申请查询系统',
        html_url,
        width=1200,
        height=800,
        min_size=(800, 600),
        js_api=api,
        maximized=True  # 默认最大化显示
    )
    return window

if __name__ == '__main__':
    # 创建窗口
    window = create_app()

    # 启动应用（关闭调试模式）
    webview.start(debug=False)
