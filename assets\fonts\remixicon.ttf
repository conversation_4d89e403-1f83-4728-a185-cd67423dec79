#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装批量上传发票所需的依赖库
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        if description:
            print(f"  用途: {description}")
        
        result = subprocess.run([sys.executable, "-m", "pip", "install", package_name], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 安装 {package_name} 时出错: {str(e)}")
        return False

def check_package(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def main():
    print("=== 批量上传发票依赖库安装程序 ===")
    print()
    
    # 定义需要安装的包
    packages = [
        {
            "name": "DrissionPage",
            "import_name": "DrissionPage",
            "description": "浏览器自动化核心库",
            "required": True
        },
        {
            "name": "pyautogui",
            "import_name": "pyautogui", 
            "description": "处理系统文件对话框",
            "required": False
        },
        {
            "name": "pyperclip",
            "import_name": "pyperclip",
            "description": "剪贴板操作支持",
            "required": False
        },
        {
            "name": "opencv-python",
            "import_name": "cv2",
            "description": "图像识别支持（可选）",
            "required": False
        },
        {
            "name": "pillow",
            "import_name": "PIL",
            "description": "图像处理支持（可选）",
            "required": False
        }
    ]
    
    # 检查已安装的包
    print("检查已安装的包...")
    installed_packages = []
    missing_packages = []
    
    for pkg in packages:
        if check_package(pkg["import_name"]):
            print(f"✅ {pkg['name']} 已安装")
            installed_packages.append(pkg)
        else:
            print(f"❌ {pkg['name']} 未安装")
            missing_packages.append(pkg)
    
    print()
    
    if not missing_packages:
        print("🎉 所有依赖库都已安装！")
        return
    
    # 安装缺失的包
    print(f"需要安装 {len(missing_packages)} 个包:")
    for pkg in missing_packages:
        status = "必需" if pkg["required"] else "可选"
        print(f"  - {pkg['name']} ({status}): {pkg['description']}")
    
    print()
    
    # 询问用户是否继续
    response = input("是否继续安装？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("安装已取消")
        return
    
    print()
    print("开始安装...")
    
    success_count = 0
    failed_packages = []
    
    for pkg in missing_packages:
        if install_package(pkg["name"], pkg["description"]):
            success_count += 1
        else:
            failed_packages.append(pkg)
        print()
    
    # 安装结果总结
    print("=== 安装结果 ===")
    print(f"成功安装: {success_count} 个")
    print(f"安装失败: {len(failed_packages)} 个")
    
    if failed_packages:
        print("\n安装失败的包:")
        for pkg in failed_packages:
            status = "必需" if pkg["required"] else "可选"
            print(f"  - {pkg['name']} ({status})")
        
        # 检查是否有必需包安装失败
        required_failed = [pkg for pkg in failed_packages if pkg["required"]]
        if required_failed:
            print("\n⚠️ 警告: 有必需的包安装失败，程序可能无法正常运行")
            print("请手动安装这些包:")
            for pkg in required_failed:
                print(f"  pip install {pkg['name']}")
        else:
            print("\n✅ 所有必需的包都已安装，程序可以正常运行")
            print("可选包的缺失只会影响部分高级功能")
    else:
        print("\n🎉 所有包都安装成功！")
    
    print("\n=== 功能说明 ===")
    print("DrissionPage: 浏览器自动化的核心库，必须安装")
    print("pyautogui: 用于处理系统文件选择对话框，推荐安装")
    print("pyperclip: 用于剪贴板操作，提高文件路径输入的准确性")
    print("opencv-python + pillow: 用于图像识别，可以提高界面元素定位的准确性")
    
    print("\n程序将使用以下文件上传策略:")
    print("1. 优先尝试直接操作文件输入框（无需额外库）")
    print("2. 如果失败，使用剪贴板方法（需要pyperclip）")
    print("3. 最后使用pyautogui处理系统对话框（需要pyautogui）")

if __name__ == "__main__":
    main()
