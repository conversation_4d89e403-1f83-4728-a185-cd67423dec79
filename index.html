<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发票申请查询系统</title>
    <script src="assets/tailwind.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: { primary: "#3b82f6", secondary: "#6b7280" },
                    borderRadius: {
                        none: "0px",
                        sm: "4px",
                        DEFAULT: "8px",
                        md: "12px",
                        lg: "16px",
                        xl: "20px",
                        "2xl": "24px",
                        "3xl": "32px",
                        full: "9999px",
                        button: "8px",
                    },
                },
            },
        };
    </script>

    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        body {
            background-color: #f9fafb;
        }
        .table-container {
            overflow-x: auto;
            max-width: 100%;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 800px; /* 确保表格最小宽度 */
        }
        th {
            cursor: pointer;
            user-select: none;
            position: relative;
        }
        .sort-icon {
            margin-left: 4px;
        }
        input:focus {
            outline: none;
        }
        textarea:focus {
            outline: none;
        }
        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        /* 响应式容器 */
        .responsive-container {
            width: 100%;
            max-width: 100vw;
            margin: 0 auto;
            padding: 0.5rem;
        }
        /* 小屏幕适配 */
        @media (min-width: 640px) {
            .responsive-container {
                padding: 1rem;
            }
        }
        @media (min-width: 768px) {
            .responsive-container {
                padding: 1.5rem;
            }
        }
        @media (min-width: 1024px) {
            .responsive-container {
                padding: 2rem;
                max-width: 1200px;
            }
        }
        @media (min-width: 1280px) {
            .responsive-container {
                max-width: 1400px;
            }
        }
        @media (min-width: 1536px) {
            .responsive-container {
                max-width: 1600px;
            }
        }
        /* 表格响应式 */
        @media (max-width: 768px) {
            .table-container {
                font-size: 0.875rem;
            }
            th, td {
                padding: 0.5rem !important;
            }
            .mobile-hidden {
                display: none;
            }
        }
        /* 合并内容样式 */
        .merged-content {
            max-height: 80px;
            overflow-y: auto;
            line-height: 1.4;
        }
        .merged-content::-webkit-scrollbar {
            width: 4px;
        }
        .merged-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        .merged-content::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        .merged-content::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        /* 按钮组响应式 */
        @media (max-width: 640px) {
            .button-group {
                flex-direction: column;
                gap: 0.5rem;
            }
            .button-group button {
                width: 100%;
            }
        }
        /* 筛选按钮激活状态 */
        .filter-button-active {
            background-color: #3b82f6 !important;
            color: white !important;
            border-color: #3b82f6 !important;
        }
        .filter-button-active:hover {
            background-color: #2563eb !important;
        }
        /* 识别号不符按钮特殊样式 */
        #filterInvalidTaxId {
            color: #dc2626 !important;
            border-color: #fca5a5 !important;
        }
        #filterInvalidTaxId:hover {
            background-color: #fef2f2 !important;
            border-color: #dc2626 !important;
        }

        #filterInvalidTaxId.filter-button-active {
            background-color: #dc2626 !important;
            color: white !important;
            border-color: #dc2626 !important;
        }
        #filterInvalidTaxId.filter-button-active:hover {
            background-color: #b91c1c !important;
        }
        /* 操作按钮样式优化 */
        .get-recipient-btn, .get-code-btn {
            padding: 4px 6px;
            border-radius: 4px;
            transition: all 0.2s ease;
            font-size: 14px;
        }
        .get-recipient-btn:hover {
            background-color: #f3e8ff;
        }
        .get-code-btn:hover {
            background-color: #fff7ed;
        }
        .get-recipient-btn:disabled, .get-code-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        /* 操作列宽度优化 */
        .operations-column {
            min-width: 120px;
        }
        /* 编辑模式样式 */
        .edit-mode {
            background-color: #fef3c7 !important;
        }
        .edit-input {
            width: 100%;
            padding: 2px 4px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: inherit;
            background-color: white;
        }
        .edit-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }
        .edit-textarea {
            width: 100%;
            min-height: 60px;
            padding: 4px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: inherit;
            background-color: white;
            resize: vertical;
        }
        .edit-textarea:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
        }
        /* 纳税人识别号验证样式 */
        .invalid-tax-id {
            color: #dc2626 !important;
            font-weight: 600;
        }
        .invalid-tax-id-bg {
            background-color: #fef2f2 !important;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="responsive-container min-h-screen">
        <div class="mb-4 sm:mb-6">
            <div class="inline-flex p-1 bg-gray-100 rounded-full w-full sm:w-auto">
                <button id="invoiceTab" class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full bg-white shadow text-gray-800">发票管理</button>
                <button id="batchUploadTab" class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full text-gray-600 hover:text-gray-800">批量上传</button>
                <button id="specTab" class="flex-1 sm:flex-none px-3 sm:px-4 py-2 text-sm font-medium rounded-full text-gray-600 hover:text-gray-800">规格设置</button>
            </div>
        </div>

        <div id="invoiceContent">
            <!-- Cookie配置区域 -->
            <div class="bg-white rounded shadow-sm mb-4 sm:mb-6 p-4 sm:p-6">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary/10 rounded-full mr-3">
                        <i class="ri-lock-line text-primary"></i>
                    </div>
                    <h2 class="text-lg sm:text-xl font-medium text-gray-800">Cookie 配置</h2>
                </div>

                <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 button-group">
                    <button id="autoLogin" class="bg-green-600 text-white px-4 py-2 rounded-button flex items-center justify-center">
                        <i class="ri-login-box-line mr-1"></i>
                        <span class="whitespace-nowrap">自动登录</span>
                    </button>
                    <button id="queryData" class="bg-blue-600 text-white px-4 py-2 rounded-button flex items-center justify-center">
                        <i class="ri-search-line mr-1"></i>
                        <span class="whitespace-nowrap">查询数据</span>
                    </button>
                    <button id="clearCookie" class="bg-gray-600 text-white px-4 py-2 rounded-button flex items-center justify-center">
                        <i class="ri-delete-bin-line mr-1"></i>
                        <span class="whitespace-nowrap">清除Cookie</span>
                    </button>

                </div>
                <div id="status" class="mt-4 hidden"></div>
            </div>

            <!-- 发票数据区域 -->
            <div class="bg-white rounded shadow-sm p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 space-y-4 sm:space-y-0">
                    <div class="flex items-center">
                        <div class="w-8 h-8 flex items-center justify-center bg-primary/10 rounded-full mr-3">
                            <i class="ri-file-list-line text-primary"></i>
                        </div>
                        <h2 class="text-lg sm:text-xl font-medium text-gray-800">发票申请数据</h2>
                    </div>
                    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
                        <div class="relative w-full sm:w-auto">
                            <input type="text" id="searchInput" class="w-full sm:w-64 pl-9 pr-3 py-2 border border-gray-300 rounded text-sm" placeholder="搜索发票信息...">
                            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 flex items-center justify-center">
                                <i class="ri-search-line text-gray-400"></i>
                            </div>
                        </div>
                        <div class="flex space-x-2 sm:space-x-3 button-group">
                            <button id="refreshData" class="flex-1 sm:flex-none bg-gray-600 text-white px-4 py-2 rounded-button flex items-center justify-center">
                                <i class="ri-refresh-line mr-1"></i>
                                <span class="whitespace-nowrap">刷新</span>
                            </button>
                            <button id="exportExcel" class="flex-1 sm:flex-none bg-green-600 text-white px-4 py-2 rounded-button flex items-center justify-center" disabled>
                                <i class="ri-file-excel-line mr-1"></i>
                                <span class="whitespace-nowrap">导出Excel</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 筛选操作栏 -->
                <div id="filterBar" class="mb-4 p-3 bg-blue-50 rounded border hidden">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium text-gray-700 mr-2">类型筛选:</span>
                            <div class="inline-flex rounded-md shadow-sm" role="group">
                                <button id="filterAll" type="button" class="px-3 py-1.5 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-l-md hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 active">
                                    全部
                                </button>
                                <button id="filterEnterprise" type="button" class="px-3 py-1.5 text-xs font-medium text-gray-900 bg-white border-t border-b border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700">
                                    企业
                                </button>
                                <button id="filterPersonal" type="button" class="px-3 py-1.5 text-xs font-medium text-gray-900 bg-white border-t border-b border-gray-200 hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700">
                                    个人
                                </button>
                                <button id="filterInvalidTaxId" type="button" class="px-3 py-1.5 text-xs font-medium text-gray-900 bg-white border border-gray-200 rounded-r-md hover:bg-gray-100 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700" title="筛选识别号不符且用户名为空的数据">
                                    识别号不符
                                </button>
                            </div>
                        </div>
                        <div class="text-sm text-gray-600">
                            显示 <span id="filteredCount">0</span> 项，共 <span id="totalDataCount">0</span> 项
                        </div>
                    </div>
                </div>

                <!-- 选择操作栏 -->
                <div id="selectionBar" class="mb-4 p-3 bg-gray-50 rounded border hidden">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="selectAll" class="mr-2" checked>
                                <span class="text-sm text-gray-700">全选</span>
                            </label>
                            <button id="selectNone" class="text-sm text-blue-600 hover:text-blue-800">全不选</button>
                            <button id="selectReverse" class="text-sm text-blue-600 hover:text-blue-800">反选</button>
                            <button id="batchEdit" class="text-sm text-orange-600 hover:text-orange-800">批量编辑</button>
                            <button id="saveAllEdits" class="text-sm text-green-600 hover:text-green-800 hidden">保存全部</button>
                            <button id="cancelAllEdits" class="text-sm text-gray-600 hover:text-gray-800 hidden">取消全部</button>
                        </div>
                        <div class="text-sm text-gray-600">
                            已选择 <span id="selectedCount">0</span> 项，共 <span id="totalCount">0</span> 项
                        </div>
                    </div>
                </div>

                <div class="table-container mb-4">
                    <table id="invoiceTable" class="min-w-full">
                        <thead>
                            <tr class="bg-gray-50 border-b">
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 w-12">
                                    <input type="checkbox" id="headerSelectAll" checked>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="sequence">
                                    <div class="flex items-center">
                                        <span>序号</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="orderId">
                                    <div class="flex items-center">
                                        <span>订单号</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="invoiceTitle">
                                    <div class="flex items-center">
                                        <span>购买方名称</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 mobile-hidden" data-sort="payerRegisterNo">
                                    <div class="flex items-center">
                                        <span>购买方纳税人识别号</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="itemName">
                                    <div class="flex items-center">
                                        <span>项目名称</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 mobile-hidden" data-sort="specification">
                                    <div class="flex items-center">
                                        <span>规格型号</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="quantity">
                                    <div class="flex items-center">
                                        <span>数量</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700" data-sort="amount">
                                    <div class="flex items-center">
                                        <span>金额</span>
                                        <span class="sort-icon ml-1"><i class="ri-arrow-up-down-line"></i></span>
                                    </div>
                                </th>
                                <th class="px-4 py-3 text-left text-sm font-medium text-gray-700 operations-column">
                                    <span>操作</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="invoiceData">
                            <!-- 数据将通过JavaScript动态填充 -->
                        </tbody>
                    </table>
                </div>

                <!-- 空数据状态 -->
                <div id="emptyState" class="flex flex-col items-center justify-center py-12">
                    <div class="w-16 h-16 flex items-center justify-center mb-4">
                        <i class="ri-file-list-line text-gray-400 ri-3x"></i>
                    </div>
                    <p class="text-gray-500 mb-2">暂无数据</p>
                    <p class="text-gray-400 text-sm">请先点击"自动登录"按钮完成登录，然后点击"查询数据"按钮获取发票申请信息</p>
                </div>

                <!-- 统计信息 -->
                <div id="statisticsBar" class="mt-4 sm:mt-6 text-sm text-gray-600 text-center sm:text-left hidden">
                    共 <span id="totalItems">0</span> 条记录
                </div>
            </div>
        </div>

        <div id="batchUploadContent" class="hidden">
            <!-- 页面标题 -->
            <div class="bg-white rounded shadow-sm p-4 sm:p-6 mb-4 sm:mb-6">
                <div class="flex items-center mb-2">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary/10 rounded-full mr-3">
                        <i class="ri-upload-cloud-line text-primary"></i>
                    </div>
                    <h2 class="text-lg sm:text-xl font-medium text-gray-800">批量上传发票</h2>
                </div>
                <p class="text-gray-600">自动化批量上传发票到天猫供应商后台</p>
            </div>

            <!-- 控制面板 -->
            <div class="bg-white rounded shadow-sm p-4 sm:p-6 mb-4 sm:mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <!-- 文件夹选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择发票文件夹</label>
                        <button id="selectFolderBtn" class="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors">
                            <i class="ri-folder-line mr-2"></i>选择文件夹
                        </button>
                        <div id="folderPathDiv" class="mt-2 text-sm text-gray-600"></div>
                    </div>

                    <!-- 操作按钮 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">操作控制</label>
                        <div class="flex space-x-2">
                            <button id="startUploadBtn" class="flex-1 bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors disabled:bg-gray-400" disabled>
                                <i class="ri-play-line mr-1"></i>开始上传
                            </button>
                            <button id="pauseUploadBtn" class="flex-1 bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors disabled:bg-gray-400" disabled>
                                <i class="ri-pause-line mr-1"></i>暂停
                            </button>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">统计信息</label>
                        <div class="grid grid-cols-2 gap-2 text-sm">
                            <div class="bg-gray-50 p-2 rounded text-center">
                                <div class="text-gray-500">总数</div>
                                <div id="totalCountDiv" class="font-semibold text-gray-900">0</div>
                            </div>
                            <div class="bg-green-50 p-2 rounded text-center">
                                <div class="text-green-600">成功</div>
                                <div id="successCountDiv" class="font-semibold text-green-700">0</div>
                            </div>
                            <div class="bg-red-50 p-2 rounded text-center">
                                <div class="text-red-600">失败</div>
                                <div id="errorCountDiv" class="font-semibold text-red-700">0</div>
                            </div>
                            <div class="bg-yellow-50 p-2 rounded text-center">
                                <div class="text-yellow-600">跳过</div>
                                <div id="skipCountDiv" class="font-semibold text-yellow-700">0</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 进度条 -->
                <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                        <span>上传进度</span>
                        <span id="progressTextDiv">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBarDiv" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <!-- 当前状态 -->
                <div id="currentStatusDiv" class="text-sm text-gray-600">
                    <i class="ri-information-line mr-1"></i>请选择包含发票PDF文件的文件夹开始
                </div>
            </div>

            <!-- 任务列表 -->
            <div class="bg-white rounded shadow-sm p-4 sm:p-6">
                <!-- 标签页导航 -->
                <div class="border-b border-gray-200 mb-4">
                    <div class="flex space-x-8">
                        <button id="allTasksTabBtn" class="tab-button py-2 px-1 border-b-2 font-medium text-sm border-blue-500 text-blue-600">
                            全部任务
                            <span id="allTasksCountSpan" class="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                        </button>
                        <button id="completedTasksTabBtn" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            已完成
                            <span id="completedTasksCountSpan" class="ml-2 px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full">0</span>
                        </button>
                    </div>
                </div>

                <!-- 任务内容区域 -->
                <div class="relative">
                    <!-- 全部任务 -->
                    <div id="allTasksContentDiv">
                        <div id="allTasksListDiv" class="max-h-96 overflow-y-auto">
                            <div class="p-8 text-center text-gray-500">
                                <i class="ri-inbox-line text-4xl mb-2"></i>
                                <p>暂无任务，请先选择文件夹</p>
                            </div>
                        </div>
                    </div>

                    <!-- 已完成任务 -->
                    <div id="completedTasksContentDiv" class="hidden">
                        <div id="completedTasksListDiv" class="max-h-96 overflow-y-auto">
                            <div class="p-8 text-center text-gray-500">
                                <i class="ri-check-double-line text-4xl mb-2"></i>
                                <p>暂无已完成的任务</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="specContent" class="hidden">
            <div class="bg-white rounded shadow-sm p-4 sm:p-6">
                <div class="flex items-center mb-4 sm:mb-6">
                    <div class="w-8 h-8 flex items-center justify-center bg-primary/10 rounded-full mr-3">
                        <i class="ri-settings-3-line text-primary"></i>
                    </div>
                    <h2 class="text-lg sm:text-xl font-medium text-gray-800">规格设置</h2>
                </div>

                <!-- 添加新规格映射 -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-md font-medium text-gray-900 mb-4">添加规格映射</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">映射关键词</label>
                            <input type="text" id="productName" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="输入项目名称中的关键词">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规格型号</label>
                            <input type="text" id="specModel" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="输入对应的规格型号">
                        </div>
                        <div class="flex items-end">
                            <button id="addSpecBtn" class="w-full bg-primary text-white px-4 py-2 rounded-button">
                                <i class="ri-add-line mr-1"></i>
                                添加映射
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 规格映射列表 -->
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-900">已保存的规格映射</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full border border-gray-200 rounded-lg">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">映射关键词</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">规格型号</th>
                                    <th class="px-4 py-3 text-left text-sm font-medium text-gray-700">操作</th>
                                </tr>
                            </thead>
                            <tbody id="specTableBody" class="divide-y divide-gray-200">
                                <!-- 动态填充 -->
                            </tbody>
                        </table>
                    </div>
                    <div id="emptySpecState" class="text-center py-8 text-gray-500">
                        <i class="ri-inbox-line text-4xl mb-2"></i>
                        <p>暂无规格映射配置</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script id="tabSwitcher">
        document.addEventListener("DOMContentLoaded", function () {
            const invoiceTab = document.getElementById("invoiceTab");
            const batchUploadTab = document.getElementById("batchUploadTab");
            const specTab = document.getElementById("specTab");
            const invoiceContent = document.getElementById("invoiceContent");
            const batchUploadContent = document.getElementById("batchUploadContent");
            const specContent = document.getElementById("specContent");

            function switchTab(activeTab, showContent) {
                // 重置所有tab
                [invoiceTab, batchUploadTab, specTab].forEach(tab => {
                    tab.classList.remove("bg-white", "shadow", "text-gray-800");
                    tab.classList.add("text-gray-600", "hover:text-gray-800");
                });

                // 隐藏所有内容
                [invoiceContent, batchUploadContent, specContent].forEach(content => {
                    content.classList.add("hidden");
                });

                // 激活选中的tab和内容
                activeTab.classList.remove("text-gray-600", "hover:text-gray-800");
                activeTab.classList.add("bg-white", "shadow", "text-gray-800");
                if (showContent) {
                    showContent.classList.remove("hidden");
                }
            }

            invoiceTab.addEventListener("click", () => {
                window.location.hash = '';
                switchTab(invoiceTab, invoiceContent);
            });

            batchUploadTab.addEventListener("click", () => {
                switchTab(batchUploadTab, batchUploadContent);
                // 初始化批量上传功能
                if (typeof initBatchUpload === 'function') {
                    initBatchUpload();
                }
            });

            specTab.addEventListener("click", () => {
                window.location.hash = 'spec';
                switchTab(specTab, specContent);
                // 切换到规格设置tab时自动加载和渲染
                setTimeout(() => {
                    if (typeof forceRenderSpecTable === 'function') {
                        forceRenderSpecTable();
                    }
                    if (typeof loadSpecConfig === 'function' && !isSpecConfigLoaded) {
                        loadSpecConfig();
                    }
                }, 50);
            });

            // 检查URL hash，如果是#spec则自动切换到规格设置
            function checkUrlHash() {
                if (window.location.hash === '#spec') {
                    switchTab(specTab, specContent);
                    setTimeout(() => {
                        if (typeof forceRenderSpecTable === 'function') {
                            forceRenderSpecTable();
                        }
                        if (typeof loadSpecConfig === 'function' && !isSpecConfigLoaded) {
                            loadSpecConfig();
                        }
                    }, 50);
                }
            }

            // 页面加载时检查hash
            checkUrlHash();

            // 监听hash变化
            window.addEventListener('hashchange', checkUrlHash);
        });
    </script>

    <script id="cookieManagement">
        document.addEventListener("DOMContentLoaded", function () {
            const autoLoginBtn = document.getElementById("autoLogin");
            const queryDataBtn = document.getElementById("queryData");
            const clearCookieBtn = document.getElementById("clearCookie");

            // 加载保存的Cookie函数
            function loadSavedCookie() {
                // 检查API是否可用
                if (typeof pywebview === 'undefined') {
                    console.log('pywebview 对象未定义，1秒后重试...');
                    setTimeout(loadSavedCookie, 1000);
                    return;
                }

                if (!pywebview.api) {
                    console.log('pywebview.api 未准备就绪，1秒后重试...');
                    setTimeout(loadSavedCookie, 1000);
                    return;
                }

                if (!pywebview.api.get_saved_cookie) {
                    console.log('get_saved_cookie 方法不存在，1秒后重试...');
                    setTimeout(loadSavedCookie, 1000);
                    return;
                }

                console.log('正在加载保存的Cookie...');

                pywebview.api.get_saved_cookie().then(function(result) {
                    console.log('API返回结果:', result);

                    if (result && result.status === 'success' && result.cookie) {
                        console.log('✅ 已自动加载保存的Cookie，长度:', result.cookie.length);
                    } else {
                        console.log('⚠️ 未找到保存的Cookie');
                    }
                }).catch(function(error) {
                    console.log('❌ 加载Cookie失败:', error);
                    // 重试一次
                    setTimeout(loadSavedCookie, 2000);
                });
            }

            // 多次尝试加载Cookie
            setTimeout(loadSavedCookie, 500);
            setTimeout(loadSavedCookie, 1500);
            setTimeout(loadSavedCookie, 3000);

            // 自动登录
            autoLoginBtn.addEventListener("click", async function () {
                try {
                    // 禁用按钮，防止重复点击
                    autoLoginBtn.disabled = true;
                    autoLoginBtn.innerHTML = '<i class="ri-loader-4-line mr-1 animate-spin"></i><span class="whitespace-nowrap">登录中...</span>';

                    showStatus('正在打开浏览器进行登录...', 'success');

                    const result = await pywebview.api.auto_login();
                    if (result.status === 'success') {
                        showStatus(result.message, 'success');

                        // 启动登录状态检查
                        const checkInterval = setInterval(async () => {
                            try {
                                const statusResult = await pywebview.api.get_login_status();
                                if (statusResult && statusResult.status === 'success' && statusResult.data) {
                                    const loginData = statusResult.data;

                                    // 检查是否登录成功
                                    if (loginData.success) {
                                        // 登录成功
                                        clearInterval(checkInterval);

                                        // Cookie已自动保存到配置文件

                                        // 显示成功消息
                                        showStatus(`✅ ${loginData.message}`, 'success');

                                        // 恢复按钮状态
                                        autoLoginBtn.disabled = false;
                                        autoLoginBtn.innerHTML = '<i class="ri-login-box-line mr-1"></i><span class="whitespace-nowrap">自动登录</span>';

                                    } else if (loginData.message && loginData.message.includes('超时') || loginData.message.includes('异常')) {
                                        // 登录失败或超时
                                        clearInterval(checkInterval);
                                        showStatus(`❌ ${loginData.message}`, 'error');

                                        // 恢复按钮状态
                                        autoLoginBtn.disabled = false;
                                        autoLoginBtn.innerHTML = '<i class="ri-login-box-line mr-1"></i><span class="whitespace-nowrap">自动登录</span>';
                                    }
                                    // 如果还在监控中，继续等待
                                }
                            } catch (error) {
                                console.log('检查登录状态失败:', error);
                            }
                        }, 3000); // 每3秒检查一次

                        // 10分钟后强制停止检查
                        setTimeout(() => {
                            clearInterval(checkInterval);
                            if (autoLoginBtn.disabled) {
                                autoLoginBtn.disabled = false;
                                autoLoginBtn.innerHTML = '<i class="ri-login-box-line mr-1"></i><span class="whitespace-nowrap">自动登录</span>';
                                showStatus('登录监控已超时，请手动检查登录状态或重试', 'error');
                            }
                        }, 600000);

                    } else {
                        showStatus(result.message, 'error');
                        // 恢复按钮状态
                        autoLoginBtn.disabled = false;
                        autoLoginBtn.innerHTML = '<i class="ri-login-box-line mr-1"></i><span class="whitespace-nowrap">自动登录</span>';
                    }
                } catch (error) {
                    showStatus('自动登录失败: ' + error.message, 'error');
                    // 恢复按钮状态
                    autoLoginBtn.disabled = false;
                    autoLoginBtn.innerHTML = '<i class="ri-login-box-line mr-1"></i><span class="whitespace-nowrap">自动登录</span>';
                }
            });

            // 查询数据
            queryDataBtn.addEventListener("click", async function () {
                console.log('开始查询数据...');

                try {
                    showStatus('正在查询数据...', 'success');

                    // 调用全局查询函数
                    const result = await window.performDataQuery();
                    if (result.status === 'success') {
                        showStatus(`查询成功！共获取到 ${result.data.totalItems} 条发票申请记录`, 'success');
                    } else {
                        showStatus(result.message, 'error');
                    }
                } catch (error) {
                    console.log('查询数据异常:', error);
                    showStatus('查询数据失败: ' + error.message, 'error');
                }
            });

            // 清除Cookie
            clearCookieBtn.addEventListener("click", function () {
                localStorage.removeItem("invoiceCookie");
                showStatus('Cookie已清除', 'success');
                // 清空数据表格
                document.getElementById("invoiceData").innerHTML = "";
                document.getElementById("emptyState").classList.remove("hidden");
                document.getElementById("filterBar").classList.add("hidden");
                document.getElementById("selectionBar").classList.add("hidden");
                document.getElementById("statisticsBar").classList.add("hidden");
            });



            // 批量上传页面按钮已移除
        });

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `mt-4 p-3 rounded-md text-sm font-medium ${
                type === 'success'
                    ? 'bg-green-50 text-green-800 border border-green-200'
                    : 'bg-red-50 text-red-800 border border-red-200'
            }`;
            status.classList.remove('hidden');

            setTimeout(() => {
                status.classList.add('hidden');
            }, 5000);
        }
    </script>

    <script id="dataManagement">
        document.addEventListener("DOMContentLoaded", function () {
            const refreshDataBtn = document.getElementById("refreshData");
            const exportExcelBtn = document.getElementById("exportExcel");
            const searchInput = document.getElementById("searchInput");
            const invoiceTable = document.getElementById("invoiceTable");
            const invoiceData = document.getElementById("invoiceData");
            const emptyState = document.getElementById("emptyState");
            const selectionBar = document.getElementById("selectionBar");
            const statisticsBar = document.getElementById("statisticsBar");
            const filterBar = document.getElementById("filterBar");

            let allInvoiceItems = [];
            let filteredData = [];
            let sortField = null;
            let sortDirection = "asc";
            let currentFilter = "all"; // 当前筛选类型: all, enterprise, personal, invalidTaxId
            let editingRows = new Set(); // 正在编辑的行索引

            // 查询数据功能已移至Cookie管理区域的查询按钮

            // 全局查询数据函数，供新的查询按钮调用
            window.performDataQuery = async function() {
                console.log('开始查询数据...');

                // 显示加载状态
                invoiceData.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center py-12">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                            <p class="mt-2 text-gray-600">正在加载数据...</p>
                        </td>
                    </tr>
                `;
                emptyState.classList.add("hidden");
                filterBar.classList.add("hidden");
                selectionBar.classList.add("hidden");
                statisticsBar.classList.add("hidden");

                try {
                    console.log('调用API获取发票数据...');
                    const result = await pywebview.api.get_invoice_data(1, 10);
                    console.log('API返回结果:', result);

                    if (result.status === 'success') {
                        const data = result.data;
                        allInvoiceItems = data.invoiceItems || [];

                        // 应用当前筛选
                        applyFilter();

                        renderData();
                        updateStatistics();
                        updateSelectionControls();

                        filterBar.classList.remove("hidden");
                        selectionBar.classList.remove("hidden");
                        statisticsBar.classList.remove("hidden");
                        exportExcelBtn.disabled = false;

                        return { status: 'success', data: data };
                    } else {
                        invoiceData.innerHTML = "";
                        emptyState.classList.remove("hidden");
                        return { status: 'error', message: result.message };
                    }
                } catch (error) {
                    console.log('查询数据异常:', error);
                    invoiceData.innerHTML = "";
                    emptyState.classList.remove("hidden");
                    return { status: 'error', message: error.message };
                }
            };

            // 刷新数据
            refreshDataBtn.addEventListener("click", async function () {
                if (typeof window.performDataQuery === 'function') {
                    const result = await window.performDataQuery();
                    if (result.status === 'success') {
                        showStatus(`刷新成功！共获取到 ${result.data.totalItems} 条发票申请记录`, 'success');
                    } else {
                        showStatus('刷新失败: ' + result.message, 'error');
                    }
                }
            });

            // 导出Excel
            exportExcelBtn.addEventListener("click", async function () {
                const selectedItems = allInvoiceItems.filter(item => item.selected);
                if (selectedItems.length === 0) {
                    showStatus('请选择要导出的数据', 'error');
                    return;
                }

                try {
                    exportExcelBtn.disabled = true;
                    exportExcelBtn.innerHTML = '<i class="ri-loader-line animate-spin mr-1"></i><span class="whitespace-nowrap">导出中...</span>';

                    const result = await pywebview.api.export_to_excel(selectedItems);

                    if (result.status === 'success') {
                        showStatus(result.message, 'success');
                    } else {
                        showStatus(result.message, 'error');
                    }
                } catch (error) {
                    showStatus('导出失败: ' + error.message, 'error');
                } finally {
                    exportExcelBtn.disabled = false;
                    exportExcelBtn.innerHTML = '<i class="ri-file-excel-line mr-1"></i><span class="whitespace-nowrap">导出Excel</span>';
                }
            });

            // 筛选功能
            function isEnterprise(item) {
                // 企业：有统一社会信用代码（payerRegisterNo不为空且长度大于0）
                return item.payerRegisterNo && item.payerRegisterNo.trim().length > 0;
            }

            // 验证纳税人识别号（用于显示样式）
            function isValidTaxId(taxId) {
                // 如果为空或null，认为是个人，不需要验证（不显示红色）
                if (!taxId || taxId.trim() === '') {
                    return true;
                }
                // 企业的统一社会信用代码应该是18位
                return taxId.trim().length === 18;
            }

            // 检查识别号是否不符合要求（用于筛选）
            function isInvalidTaxIdForFilter(taxId) {
                // 空识别号或非18位都认为是不符合要求
                if (!taxId || taxId.trim() === '') {
                    return true; // 个人发票，识别号不符
                }
                // 企业识别号不是18位也不符合要求
                return taxId.trim().length !== 18;
            }

            function applyFilter() {
                let baseData = [...allInvoiceItems];

                // 应用类型筛选
                if (currentFilter === 'enterprise') {
                    baseData = baseData.filter(item => isEnterprise(item));
                } else if (currentFilter === 'personal') {
                    baseData = baseData.filter(item => !isEnterprise(item));
                } else if (currentFilter === 'invalidTaxId') {
                    baseData = baseData.filter(item => {
                        // 识别号不符：使用专门的筛选函数
                        const invalidTaxId = isInvalidTaxIdForFilter(item.payerRegisterNo);
                        // 用户名为空：发票抬头为空或空白
                        const emptyUserName = !item.invoiceTitle || item.invoiceTitle.trim() === '';
                        // 满足任一条件：识别号不符 OR 用户名为空
                        return invalidTaxId || emptyUserName;
                    });
                }

                // 应用搜索筛选
                const searchTerm = searchInput.value.toLowerCase();
                if (searchTerm && baseData.length > 0) {
                    filteredData = baseData.filter(item =>
                        (item.orderId && item.orderId.toLowerCase().includes(searchTerm)) ||
                        (item.invoiceTitle && item.invoiceTitle.toLowerCase().includes(searchTerm)) ||
                        (item.payerRegisterNo && item.payerRegisterNo.toLowerCase().includes(searchTerm)) ||
                        (item.itemName && item.itemName.toLowerCase().includes(searchTerm)) ||
                        (item.specification && item.specification.toLowerCase().includes(searchTerm))
                    );
                } else {
                    filteredData = baseData;
                }
            }

            // 搜索功能
            searchInput.addEventListener("input", function () {
                applyFilter();
                renderData();
                updateStatistics();
            });

            // 筛选按钮事件
            const filterAllBtn = document.getElementById("filterAll");
            const filterEnterpriseBtn = document.getElementById("filterEnterprise");
            const filterPersonalBtn = document.getElementById("filterPersonal");
            const filterInvalidTaxIdBtn = document.getElementById("filterInvalidTaxId");

            function updateFilterButtons() {
                // 移除所有激活状态
                [filterAllBtn, filterEnterpriseBtn, filterPersonalBtn, filterInvalidTaxIdBtn].forEach(btn => {
                    btn.classList.remove("filter-button-active");
                });

                // 添加当前激活状态
                if (currentFilter === "all") {
                    filterAllBtn.classList.add("filter-button-active");
                } else if (currentFilter === "enterprise") {
                    filterEnterpriseBtn.classList.add("filter-button-active");
                } else if (currentFilter === "personal") {
                    filterPersonalBtn.classList.add("filter-button-active");
                } else if (currentFilter === "invalidTaxId") {
                    filterInvalidTaxIdBtn.classList.add("filter-button-active");
                }
            }

            filterAllBtn.addEventListener("click", function () {
                currentFilter = "all";
                updateFilterButtons();
                applyFilter();
                renderData();
                updateStatistics();
            });

            filterEnterpriseBtn.addEventListener("click", function () {
                currentFilter = "enterprise";
                updateFilterButtons();
                applyFilter();
                renderData();
                updateStatistics();
            });

            filterPersonalBtn.addEventListener("click", function () {
                currentFilter = "personal";
                updateFilterButtons();
                applyFilter();
                renderData();
                updateStatistics();
            });

            filterInvalidTaxIdBtn.addEventListener("click", function () {
                currentFilter = "invalidTaxId";
                updateFilterButtons();
                applyFilter();
                renderData();
                updateStatistics();
            });

            // 初始化筛选按钮状态
            updateFilterButtons();

            // 更新发票抬头函数
            function updateInvoiceTitle(index, orderId, newTitle) {
                try {
                    // 更新filteredData中的数据
                    if (filteredData[index] && filteredData[index].orderId === orderId) {
                        filteredData[index].invoiceTitle = newTitle;
                        console.log(`更新索引 ${index} 的发票抬头为: ${newTitle}`);
                    }

                    // 更新allInvoiceItems中的数据
                    const originalIndex = allInvoiceItems.findIndex(item => item.orderId === orderId);
                    if (originalIndex !== -1) {
                        allInvoiceItems[originalIndex].invoiceTitle = newTitle;
                        console.log(`更新原始数据中订单 ${orderId} 的发票抬头为: ${newTitle}`);
                    }

                    // 重新渲染表格以显示更新后的数据
                    renderData();

                    console.log(`成功更新订单 ${orderId} 的发票抬头`);
                } catch (error) {
                    console.error(`更新发票抬头失败:`, error);
                    showStatus('更新发票抬头失败', 'error');
                }
            }

            // Cookie文本框已移除，Cookie自动保存到配置文件

            // 选择控制
            const selectAllCheckbox = document.getElementById("selectAll");
            const headerSelectAllCheckbox = document.getElementById("headerSelectAll");
            const selectNoneBtn = document.getElementById("selectNone");
            const selectReverseBtn = document.getElementById("selectReverse");

            selectAllCheckbox.addEventListener("change", function () {
                const isChecked = this.checked;
                allInvoiceItems.forEach(item => item.selected = isChecked);
                headerSelectAllCheckbox.checked = isChecked;
                renderData();
                updateSelectionControls();
            });

            headerSelectAllCheckbox.addEventListener("change", function () {
                const isChecked = this.checked;
                allInvoiceItems.forEach(item => item.selected = isChecked);
                selectAllCheckbox.checked = isChecked;
                renderData();
                updateSelectionControls();
            });

            selectNoneBtn.addEventListener("click", function () {
                allInvoiceItems.forEach(item => item.selected = false);
                selectAllCheckbox.checked = false;
                headerSelectAllCheckbox.checked = false;
                renderData();
                updateSelectionControls();
            });

            selectReverseBtn.addEventListener("click", function () {
                allInvoiceItems.forEach(item => item.selected = !item.selected);
                updateSelectAllState();
                renderData();
                updateSelectionControls();
            });

            // 批量编辑功能
            const batchEditBtn = document.getElementById("batchEdit");
            const saveAllEditsBtn = document.getElementById("saveAllEdits");
            const cancelAllEditsBtn = document.getElementById("cancelAllEdits");

            batchEditBtn.addEventListener("click", function () {
                // 进入批量编辑模式，编辑所有可见的行
                editingRows.clear();
                filteredData.forEach((item, index) => {
                    editingRows.add(index);
                });

                // 切换按钮显示
                batchEditBtn.classList.add("hidden");
                saveAllEditsBtn.classList.remove("hidden");
                cancelAllEditsBtn.classList.remove("hidden");

                renderData();
            });

            saveAllEditsBtn.addEventListener("click", function () {
                // 保存所有编辑
                const editingIndexes = Array.from(editingRows);
                editingIndexes.forEach(index => {
                    saveEdit(index);
                });

                // 切换按钮显示
                batchEditBtn.classList.remove("hidden");
                saveAllEditsBtn.classList.add("hidden");
                cancelAllEditsBtn.classList.add("hidden");
            });

            cancelAllEditsBtn.addEventListener("click", function () {
                // 取消所有编辑
                editingRows.clear();

                // 切换按钮显示
                batchEditBtn.classList.remove("hidden");
                saveAllEditsBtn.classList.add("hidden");
                cancelAllEditsBtn.classList.add("hidden");

                renderData();
            });

            // 排序功能
            document.querySelectorAll("th[data-sort]").forEach((th) => {
                th.addEventListener("click", function () {
                    const field = this.getAttribute("data-sort");
                    if (field === sortField) {
                        sortDirection = sortDirection === "asc" ? "desc" : "asc";
                    } else {
                        sortField = field;
                        sortDirection = "asc";
                    }

                    // 更新排序图标
                    document.querySelectorAll(".sort-icon").forEach((icon) => {
                        icon.innerHTML = '<i class="ri-arrow-up-down-line"></i>';
                    });
                    const sortIcon = this.querySelector(".sort-icon");
                    if (sortDirection === "asc") {
                        sortIcon.innerHTML = '<i class="ri-arrow-up-line"></i>';
                    } else {
                        sortIcon.innerHTML = '<i class="ri-arrow-down-line"></i>';
                    }

                    sortData();
                    renderData();
                });
            });

            // 辅助函数
            function updateSelectAllState() {
                const selectedCount = allInvoiceItems.filter(item => item.selected).length;
                const totalCount = allInvoiceItems.length;

                if (selectedCount === 0) {
                    selectAllCheckbox.checked = false;
                    headerSelectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                    headerSelectAllCheckbox.indeterminate = false;
                } else if (selectedCount === totalCount) {
                    selectAllCheckbox.checked = true;
                    headerSelectAllCheckbox.checked = true;
                    selectAllCheckbox.indeterminate = false;
                    headerSelectAllCheckbox.indeterminate = false;
                } else {
                    selectAllCheckbox.checked = false;
                    headerSelectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = true;
                    headerSelectAllCheckbox.indeterminate = true;
                }
            }

            function updateSelectionControls() {
                const selectedCount = allInvoiceItems.filter(item => item.selected).length;
                const totalCount = allInvoiceItems.length;

                document.getElementById("selectedCount").textContent = selectedCount;
                document.getElementById("totalCount").textContent = totalCount;

                exportExcelBtn.disabled = selectedCount === 0;
            }

            function updateStatistics() {
                document.getElementById("totalItems").textContent = filteredData.length;

                // 更新筛选栏统计信息
                const filteredCountElement = document.getElementById("filteredCount");
                const totalDataCountElement = document.getElementById("totalDataCount");

                if (filteredCountElement && totalDataCountElement) {
                    filteredCountElement.textContent = filteredData.length;
                    totalDataCountElement.textContent = allInvoiceItems.length;
                }
            }

            // 排序数据
            function sortData() {
                if (!sortField || filteredData.length === 0) return;

                filteredData.sort((a, b) => {
                    let valueA = a[sortField];
                    let valueB = b[sortField];

                    // 处理数字类型
                    if (sortField === 'sequence' || sortField === 'quantity') {
                        valueA = parseInt(valueA) || 0;
                        valueB = parseInt(valueB) || 0;
                    } else if (sortField === 'amount') {
                        valueA = parseFloat(valueA) || 0;
                        valueB = parseFloat(valueB) || 0;
                    }

                    if (valueA < valueB) {
                        return sortDirection === "asc" ? -1 : 1;
                    }
                    if (valueA > valueB) {
                        return sortDirection === "asc" ? 1 : -1;
                    }
                    return 0;
                });
            }

            // 编辑功能
            function startEdit(index) {
                editingRows.add(index);
                renderData();
            }

            function cancelEdit(index) {
                editingRows.delete(index);
                renderData();
            }

            function saveEdit(index) {
                const row = document.querySelector(`tr[data-index="${index}"]`);
                if (!row) return;

                // 获取编辑后的值
                const newData = {};
                row.querySelectorAll('.edit-input, .edit-textarea').forEach(input => {
                    const field = input.dataset.field;
                    newData[field] = input.value;
                });

                // 更新数据
                const originalIndex = allInvoiceItems.findIndex(item =>
                    item.sequence === filteredData[index].sequence
                );

                if (originalIndex !== -1) {
                    // 更新原始数据
                    Object.keys(newData).forEach(field => {
                        allInvoiceItems[originalIndex][field] = newData[field];
                    });

                    // 特殊处理金额格式化
                    if (newData.amount) {
                        const amount = parseFloat(newData.amount) || 0;
                        allInvoiceItems[originalIndex].amountFormatted = `¥${amount.toFixed(2)}`;
                    }

                    // 更新筛选数据
                    Object.keys(newData).forEach(field => {
                        filteredData[index][field] = newData[field];
                    });

                    if (newData.amount) {
                        const amount = parseFloat(newData.amount) || 0;
                        filteredData[index].amountFormatted = `¥${amount.toFixed(2)}`;
                    }
                }

                // 退出编辑模式
                editingRows.delete(index);
                renderData();
            }

            // 创建行HTML
            function createRowHTML(item, index, isEditing) {
                const escapedInvoiceTitle = (item.invoiceTitle || '').replace(/"/g, '&quot;');
                const escapedPayerRegisterNo = (item.payerRegisterNo || '').replace(/"/g, '&quot;');
                const escapedItemName = (item.itemName || '').replace(/"/g, '&quot;');
                const escapedSpecification = (item.specification || '').replace(/"/g, '&quot;');
                const escapedQuantity = (item.quantity || '').replace(/"/g, '&quot;');
                const escapedAmount = (item.amount || '').replace(/"/g, '&quot;');

                // 验证纳税人识别号
                const isValidTax = isValidTaxId(item.payerRegisterNo);
                const taxIdClass = !isValidTax ? 'invalid-tax-id' : '';
                const taxIdBgClass = !isValidTax ? 'invalid-tax-id-bg' : '';

                return `
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-center">
                        <input type="checkbox" class="item-checkbox" data-index="${index}" ${item.selected ? 'checked' : ''}>
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700">${item.sequence}</td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 font-mono">${item.orderId}</td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 max-w-xs" title="${item.invoiceTitle}">
                        ${isEditing ?
                            `<input type="text" class="edit-input" data-field="invoiceTitle" value="${escapedInvoiceTitle}">` :
                            `<div class="truncate">${item.invoiceTitle || (isValidTax ? '' : '<span class="text-gray-400 italic">点击获取收件人按钮获取姓名</span>')}</div>`
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 font-mono mobile-hidden ${taxIdBgClass}">
                        ${isEditing ?
                            `<input type="text" class="edit-input ${taxIdClass}" data-field="payerRegisterNo" value="${escapedPayerRegisterNo}" maxlength="18" title="统一社会信用代码应为18位">` :
                            `<span class="${taxIdClass}" title="${!isValidTax && item.payerRegisterNo ? '统一社会信用代码应为18位' : ''}">${item.payerRegisterNo}</span>`
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 max-w-xs" title="${item.itemName}">
                        ${isEditing ?
                            `<textarea class="edit-textarea" data-field="itemName">${escapedItemName}</textarea>` :
                            `<div class="merged-content">
                                ${item.itemName.split(';').map(name => `<div class="mb-1">${name.trim()}</div>`).join('')}
                            </div>`
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 mobile-hidden">
                        ${isEditing ?
                            `<textarea class="edit-textarea" data-field="specification">${escapedSpecification}</textarea>` :
                            `<div class="merged-content">
                                ${item.specification.split(';').map(spec => `<div class="mb-1">${spec.trim()}</div>`).join('')}
                            </div>`
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 text-center">
                        ${isEditing ?
                            `<input type="number" class="edit-input" data-field="quantity" value="${escapedQuantity}" min="0">` :
                            item.quantity
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm text-gray-700 font-medium text-green-600">
                        ${isEditing ?
                            `<input type="number" class="edit-input" data-field="amount" value="${escapedAmount}" min="0" step="0.01">` :
                            item.amountFormatted
                        }
                    </td>
                    <td class="px-2 sm:px-4 py-2 sm:py-3 text-xs sm:text-sm operations-column">
                        ${isEditing ?
                            `<div class="flex flex-wrap gap-1">
                                <button class="save-btn text-green-600 hover:text-green-800" data-index="${index}" title="保存">
                                    <i class="ri-check-line"></i>
                                </button>
                                <button class="cancel-btn text-gray-600 hover:text-gray-800" data-index="${index}" title="取消">
                                    <i class="ri-close-line"></i>
                                </button>
                            </div>` :
                            `<div class="flex flex-wrap gap-1">
                                <button class="edit-btn text-blue-600 hover:text-blue-800" data-index="${index}" title="编辑">
                                    <i class="ri-edit-line"></i>
                                </button>
                                <button class="get-recipient-btn text-purple-600 hover:text-purple-800" data-index="${index}" data-order-id="${item.orderId}" title="获取收件人">
                                    <i class="ri-user-line"></i>
                                </button>
                                <button class="get-code-btn text-orange-600 hover:text-orange-800" data-index="${index}" data-order-id="${item.orderId}" title="获取识别码">
                                    <i class="ri-qr-code-line"></i>
                                </button>
                            </div>`
                        }
                    </td>
                `;
            }

            // 渲染数据
            function renderData() {
                invoiceData.innerHTML = "";

                if (!filteredData || filteredData.length === 0) {
                    emptyState.classList.remove("hidden");
                    return;
                }

                emptyState.classList.add("hidden");

                // 渲染表格数据
                filteredData.forEach((item, index) => {
                    const row = document.createElement("tr");
                    const isEditing = editingRows.has(index);
                    row.className = `border-b hover:bg-gray-50 ${isEditing ? 'edit-mode' : ''}`;
                    row.dataset.index = index;

                    row.innerHTML = createRowHTML(item, index, isEditing);
                    invoiceData.appendChild(row);
                });

                // 添加复选框事件监听
                document.querySelectorAll('.item-checkbox').forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const index = parseInt(this.dataset.index);
                        const originalIndex = allInvoiceItems.findIndex(item =>
                            item.sequence === filteredData[index].sequence
                        );
                        if (originalIndex !== -1) {
                            allInvoiceItems[originalIndex].selected = this.checked;
                        }
                        updateSelectAllState();
                        updateSelectionControls();
                    });
                });

                // 添加编辑按钮事件监听
                document.querySelectorAll('.edit-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        startEdit(index);
                    });
                });

                // 添加保存按钮事件监听
                document.querySelectorAll('.save-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        saveEdit(index);
                    });
                });

                // 添加取消按钮事件监听
                document.querySelectorAll('.cancel-btn').forEach(btn => {
                    btn.addEventListener('click', function() {
                        const index = parseInt(this.dataset.index);
                        cancelEdit(index);
                    });
                });

                // 添加键盘快捷键支持
                document.querySelectorAll('.edit-input, .edit-textarea').forEach(input => {
                    input.addEventListener('keydown', function(e) {
                        const row = this.closest('tr');
                        const index = parseInt(row.dataset.index);

                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            saveEdit(index);
                        } else if (e.key === 'Escape') {
                            e.preventDefault();
                            cancelEdit(index);
                        }
                    });

                    // 为纳税人识别号输入框添加实时验证
                    if (input.dataset.field === 'payerRegisterNo') {
                        input.addEventListener('input', function() {
                            const value = this.value.trim();
                            const isValid = isValidTaxId(value);

                            // 更新样式
                            if (value && !isValid) {
                                this.classList.add('invalid-tax-id');
                                this.title = '统一社会信用代码应为18位';
                            } else {
                                this.classList.remove('invalid-tax-id');
                                this.title = '';
                            }
                        });
                    }
                });

                // 添加获取收件人按钮事件监听
                document.querySelectorAll('.get-recipient-btn').forEach(btn => {
                    btn.addEventListener('click', async function() {
                        const orderId = this.dataset.orderId;
                        const index = parseInt(this.dataset.index);

                        try {
                            // 检查当前用户名是否已经存在
                            const currentItem = filteredData[index];
                            if (currentItem && currentItem.invoiceTitle && currentItem.invoiceTitle.trim() !== '') {
                                console.log(`订单 ${orderId} 已有用户名: ${currentItem.invoiceTitle}，跳过获取`);
                                return; // 已有用户名，不做处理
                            }

                            // 显示加载状态
                            this.innerHTML = '<i class="ri-loader-line animate-spin"></i>';
                            this.disabled = true;

                            const result = await pywebview.api.get_recipient_info(orderId);

                            if (result.status === 'success') {
                                // 不显示提示信息，静默处理

                                // 如果获取到了用户真实姓名，更新表格数据
                                if (result.action === 'update_name' && result.fullName) {
                                    updateInvoiceTitle(index, orderId, result.fullName);
                                }

                                // Cookie已自动保存到配置文件
                            } else {
                                // 只在真正出错时显示错误信息
                                showStatus(result.message, 'error');
                            }
                        } catch (error) {
                            showStatus('获取收件人信息失败: ' + error.message, 'error');
                        } finally {
                            // 恢复按钮状态
                            this.innerHTML = '<i class="ri-user-line"></i>';
                            this.disabled = false;
                        }
                    });
                });

                // 添加获取识别码按钮事件监听
                document.querySelectorAll('.get-code-btn').forEach(btn => {
                    btn.addEventListener('click', async function() {
                        const orderId = this.dataset.orderId;
                        const index = parseInt(this.dataset.index);

                        try {
                            // 获取当前行的公司名称
                            const currentItem = filteredData[index];
                            if (!currentItem || !currentItem.invoiceTitle) {
                                showStatus('请先获取公司名称', 'error');
                                return;
                            }

                            const companyName = currentItem.invoiceTitle.trim();
                            if (companyName === '') {
                                showStatus('公司名称为空，无法查询识别码', 'error');
                                return;
                            }

                            // 显示加载状态
                            this.innerHTML = '<i class="ri-loader-line animate-spin"></i>';
                            this.disabled = true;

                            const result = await pywebview.api.get_company_code(companyName);

                            if (result.status === 'success') {
                                showStatus(result.message, 'success');
                            } else {
                                showStatus(result.message, 'error');
                            }
                        } catch (error) {
                            showStatus('获取识别码失败: ' + error.message, 'error');
                        } finally {
                            // 恢复按钮状态
                            this.innerHTML = '<i class="ri-qr-code-line"></i>';
                            this.disabled = false;
                        }
                    });
                });
            }

            // 自动查询功能已移除，用户需要手动点击"查询数据"按钮
        });
    </script>

    <script id="specManagement">
        // 全局变量
        let specMappings = [];
        let addSpecBtn, productNameInput, specModelInput, specTableBody, emptySpecState;
        let isSpecConfigLoaded = false;

        // 加载规格配置
        async function loadSpecConfig() {
            try {
                // 确保pywebview API已准备好
                if (typeof pywebview === 'undefined' || !pywebview.api) {
                    setTimeout(loadSpecConfig, 200);
                    return;
                }

                const result = await pywebview.api.load_spec_config();
                if (result.status === 'success') {
                    specMappings = result.data;
                    isSpecConfigLoaded = true;
                    // 确保DOM元素存在后再渲染
                    setTimeout(() => {
                        renderSpecTable();
                    }, 50);
                }
            } catch (error) {
                // 如果失败，重试
                setTimeout(loadSpecConfig, 500);
            }
        }

        // 立即开始尝试加载配置（不等待DOM）
        loadSpecConfig();

        // 渲染规格表格函数（全局作用域）
        function renderSpecTable() {
            // 重新获取DOM元素（防止元素丢失）
            if (!specTableBody) {
                specTableBody = document.getElementById("specTableBody");
            }
            if (!emptySpecState) {
                emptySpecState = document.getElementById("emptySpecState");
            }

            if (!specTableBody || !emptySpecState) {
                setTimeout(renderSpecTable, 100);
                return;
            }

            specTableBody.innerHTML = '';

            if (!specMappings || specMappings.length === 0) {
                emptySpecState.classList.remove('hidden');
                return;
            }

            emptySpecState.classList.add('hidden');

            specMappings.forEach((mapping, index) => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                row.innerHTML = `
                    <td class="px-4 py-3 text-sm text-gray-700">${mapping.productName || ''}</td>
                    <td class="px-4 py-3 text-sm text-gray-700">${mapping.specModel || ''}</td>
                    <td class="px-4 py-3 text-sm">
                        <button onclick="removeSpecMapping(${index})" class="text-red-600 hover:text-red-800">
                            <i class="ri-delete-bin-line mr-1"></i>
                            删除
                        </button>
                    </td>
                `;
                specTableBody.appendChild(row);
            });
        }

        // 全局强制渲染函数
        window.forceRenderSpecTable = function() {
            renderSpecTable();
        };

        // 初始化DOM元素和事件监听器
        function initSpecManagement() {
            // 获取DOM元素（即使在隐藏状态下也要获取）
            addSpecBtn = document.getElementById("addSpecBtn");
            productNameInput = document.getElementById("productName");
            specModelInput = document.getElementById("specModel");
            specTableBody = document.getElementById("specTableBody");
            emptySpecState = document.getElementById("emptySpecState");

            if (!addSpecBtn || !specTableBody || !emptySpecState) {
                setTimeout(initSpecManagement, 100);
                return;
            }

            // 强制渲染表格（无论配置是否加载）
            renderSpecTable();

            // 如果配置还没加载，尝试加载
            if (!isSpecConfigLoaded) {
                loadSpecConfig();
            }





            // 添加规格映射
            addSpecBtn.addEventListener("click", async function () {
                const productName = productNameInput.value.trim();
                const specModel = specModelInput.value.trim();

                if (!productName || !specModel) {
                    showStatus('请输入映射关键词和规格型号', 'error');
                    return;
                }

                try {
                    addSpecBtn.disabled = true;
                    addSpecBtn.innerHTML = '<i class="ri-loader-line animate-spin mr-1"></i>添加中...';

                    // 调用API添加映射并自动保存
                    const result = await pywebview.api.add_spec_mapping(productName, specModel);

                    if (result.status === 'success') {
                        // 更新本地数据
                        specMappings = result.data;

                        // 清空输入框
                        productNameInput.value = '';
                        specModelInput.value = '';

                        // 重新渲染表格
                        renderSpecTable();

                        showStatus(result.message, 'success');
                    } else {
                        showStatus(result.message, 'error');
                    }
                } catch (error) {
                    showStatus('添加映射失败: ' + error.message, 'error');
                } finally {
                    addSpecBtn.disabled = false;
                    addSpecBtn.innerHTML = '<i class="ri-add-line mr-1"></i>添加映射';
                }
            });

            // 删除规格映射
            window.removeSpecMapping = async function(index) {
                if (confirm('确定要删除这个映射关系吗？')) {
                    try {
                        // 调用API删除映射并自动保存
                        const result = await pywebview.api.remove_spec_mapping(index);

                        if (result.status === 'success') {
                            // 更新本地数据
                            specMappings = result.data;

                            // 重新渲染表格
                            renderSpecTable();

                            showStatus(result.message, 'success');
                        } else {
                            showStatus(result.message, 'error');
                        }
                    } catch (error) {
                        showStatus('删除映射失败: ' + error.message, 'error');
                    }
                }
            };
        }

        // 监听DOM加载完成
        document.addEventListener("DOMContentLoaded", function () {
            initSpecManagement();
            // 多次尝试确保成功
            setTimeout(initSpecManagement, 100);
            setTimeout(initSpecManagement, 300);
            setTimeout(initSpecManagement, 500);

            // 强制渲染
            setTimeout(() => {
                if (typeof forceRenderSpecTable === 'function') {
                    forceRenderSpecTable();
                }
            }, 200);
        });

        // 页面完全加载后也尝试初始化
        window.addEventListener('load', function() {
            setTimeout(initSpecManagement, 100);
            setTimeout(() => {
                if (typeof forceRenderSpecTable === 'function') {
                    forceRenderSpecTable();
                }
            }, 300);
        });
    </script>

    <script id="batchUploadManagement">
        // 批量上传相关变量
        let batchUploadTasks = [];
        let batchUploadCompletedTasks = [];
        let batchUploadCurrentTaskIndex = 0;
        let batchUploadIsRunning = false;
        let batchUploadIsPaused = false;
        let batchUploadFolderPath = '';
        let batchUploadActiveTab = 'all';

        // 初始化批量上传功能
        function initBatchUpload() {
            if (typeof pywebview === 'undefined') {
                setTimeout(initBatchUpload, 500);
                return;
            }

            const selectFolderBtn = document.getElementById('selectFolderBtn');
            const startUploadBtn = document.getElementById('startUploadBtn');
            const pauseUploadBtn = document.getElementById('pauseUploadBtn');
            const allTasksTabBtn = document.getElementById('allTasksTabBtn');
            const completedTasksTabBtn = document.getElementById('completedTasksTabBtn');

            if (!selectFolderBtn) return; // 如果元素不存在，说明还没切换到批量上传页面

            // 绑定事件
            selectFolderBtn.addEventListener('click', selectFolder);
            startUploadBtn.addEventListener('click', startUpload);
            pauseUploadBtn.addEventListener('click', pauseUpload);
            allTasksTabBtn.addEventListener('click', () => switchBatchUploadTab('all'));
            completedTasksTabBtn.addEventListener('click', () => switchBatchUploadTab('completed'));
        }

        // 选择文件夹
        async function selectFolder() {
            try {
                updateBatchUploadStatus('正在选择文件夹...');
                const result = await pywebview.api.select_invoice_folder();

                if (result.status === 'success') {
                    batchUploadFolderPath = result.folder_path;
                    document.getElementById('folderPathDiv').textContent = `已选择: ${batchUploadFolderPath}`;
                    batchUploadTasks = result.pdf_files;
                    renderBatchUploadTaskList();
                    updateBatchUploadStatistics();
                    document.getElementById('startUploadBtn').disabled = false;
                    updateBatchUploadStatus(`已扫描到 ${batchUploadTasks.length} 个PDF文件`);
                } else {
                    updateBatchUploadStatus(result.message, 'error');
                }
            } catch (error) {
                updateBatchUploadStatus('选择文件夹失败: ' + error.message, 'error');
            }
        }

        // 开始上传
        async function startUpload() {
            if (batchUploadIsRunning) return;

            batchUploadIsRunning = true;
            batchUploadIsPaused = false;
            batchUploadCurrentTaskIndex = 0;

            document.getElementById('startUploadBtn').disabled = true;
            document.getElementById('pauseUploadBtn').disabled = false;
            document.getElementById('selectFolderBtn').disabled = true;

            updateBatchUploadStatus('正在初始化浏览器...');

            try {
                const initResult = await pywebview.api.init_upload_browser();
                if (initResult.status !== 'success') {
                    throw new Error(initResult.message);
                }

                updateBatchUploadStatus('浏览器初始化成功，开始处理任务...');
                await processAllBatchUploadTasks();

            } catch (error) {
                updateBatchUploadStatus('上传失败: ' + error.message, 'error');
            } finally {
                finishBatchUpload();
            }
        }

        // 暂停上传
        function pauseUpload() {
            batchUploadIsPaused = !batchUploadIsPaused;

            const pauseBtn = document.getElementById('pauseUploadBtn');
            if (batchUploadIsPaused) {
                pauseBtn.innerHTML = '<i class="ri-play-line mr-1"></i>继续';
                updateBatchUploadStatus('已暂停，点击继续按钮恢复上传');
            } else {
                pauseBtn.innerHTML = '<i class="ri-pause-line mr-1"></i>暂停';
                updateBatchUploadStatus('继续处理任务...');
            }
        }

        // 处理所有任务（新的订单主导模式）
        async function processAllBatchUploadTasks() {
            try {
                updateBatchUploadStatus('开始以订单为主导的上传流程...');

                // 调用新的订单主导模式
                const result = await pywebview.api.process_orders_with_invoices(batchUploadTasks);

                if (result.status === 'success') {
                    updateBatchUploadStatus(result.message);

                    // 更新统计信息
                    const successCount = result.success_count || 0;
                    const skipCount = result.skip_count || 0;
                    const errorCount = result.error_count || 0;

                    // 模拟任务状态更新（因为新模式是批量处理）
                    simulateBatchUploadTaskStatusUpdates(successCount, skipCount, errorCount);

                } else {
                    updateBatchUploadStatus('上传失败: ' + result.message, 'error');
                }

            } catch (error) {
                updateBatchUploadStatus('上传过程中出错: ' + error.message, 'error');
            }
        }

        function simulateBatchUploadTaskStatusUpdates(successCount, skipCount, errorCount) {
            // 模拟更新任务状态，用于界面显示
            let processedCount = 0;

            // 标记成功的任务
            for (let i = 0; i < batchUploadTasks.length && processedCount < successCount; i++) {
                if (batchUploadTasks[i].status !== 'success') {
                    updateBatchUploadTaskStatus(i, 'success', '上传成功');
                    processedCount++;
                }
            }

            // 标记跳过的任务
            processedCount = 0;
            for (let i = 0; i < batchUploadTasks.length && processedCount < skipCount; i++) {
                if (batchUploadTasks[i].status !== 'success' && batchUploadTasks[i].status !== 'skipped') {
                    updateBatchUploadTaskStatus(i, 'skipped', '未找到匹配订单');
                    processedCount++;
                }
            }

            // 标记失败的任务
            processedCount = 0;
            for (let i = 0; i < batchUploadTasks.length && processedCount < errorCount; i++) {
                if (batchUploadTasks[i].status !== 'success' && batchUploadTasks[i].status !== 'skipped' && batchUploadTasks[i].status !== 'error') {
                    updateBatchUploadTaskStatus(i, 'error', '上传失败');
                    processedCount++;
                }
            }

            // 更新进度条到100%
            updateBatchUploadProgress(batchUploadTasks.length, batchUploadTasks.length);
            updateBatchUploadStatistics();
        }

        // 完成上传
        function finishBatchUpload() {
            batchUploadIsRunning = false;
            batchUploadIsPaused = false;

            document.getElementById('startUploadBtn').disabled = false;
            document.getElementById('pauseUploadBtn').disabled = true;
            document.getElementById('selectFolderBtn').disabled = false;

            const stats = calculateBatchUploadStatistics();
            updateBatchUploadStatus(`上传完成！成功: ${stats.success}, 失败: ${stats.error}, 跳过: ${stats.skipped}`);

            document.getElementById('progressBarDiv').style.width = '100%';
            document.getElementById('progressTextDiv').textContent = '100%';
        }

        // 更新状态
        function updateBatchUploadStatus(message, type = 'info') {
            const icons = {
                info: 'ri-information-line',
                error: 'ri-error-warning-line',
                success: 'ri-check-line'
            };

            const colors = {
                info: 'text-gray-600',
                error: 'text-red-600',
                success: 'text-green-600'
            };

            const statusDiv = document.getElementById('currentStatusDiv');
            if (statusDiv) {
                statusDiv.innerHTML = `<i class="${icons[type]} mr-1"></i>${message}`;
                statusDiv.className = `text-sm ${colors[type]}`;
            }
        }

        // 其他辅助函数
        function renderBatchUploadTaskList() {
            // 简化版任务列表渲染
            const allTasksList = document.getElementById('allTasksListDiv');
            if (!allTasksList) return;

            allTasksList.innerHTML = '';
            if (batchUploadTasks.length === 0) {
                allTasksList.innerHTML = `
                    <div class="p-8 text-center text-gray-500">
                        <i class="ri-inbox-line text-4xl mb-2"></i>
                        <p>暂无任务，请先选择文件夹</p>
                    </div>
                `;
            } else {
                batchUploadTasks.forEach((task, index) => {
                    const taskDiv = document.createElement('div');
                    taskDiv.className = 'bg-white border border-gray-200 rounded p-3 mb-2';
                    taskDiv.innerHTML = `
                        <div class="flex justify-between items-center">
                            <div>
                                <div class="font-medium text-sm">${task.filename}</div>
                                <div class="text-xs text-gray-500">${task.company_name}</div>
                            </div>
                            <div class="text-xs text-gray-400" id="task-status-${index}">等待中</div>
                        </div>
                    `;
                    allTasksList.appendChild(taskDiv);
                });
            }
        }

        function updateBatchUploadTaskStatus(index, status, message) {
            const statusEl = document.getElementById(`task-status-${index}`);
            if (statusEl) {
                statusEl.textContent = message;
                statusEl.className = `text-xs ${
                    status === 'success' ? 'text-green-600' :
                    status === 'error' ? 'text-red-600' :
                    status === 'processing' ? 'text-blue-600' :
                    status === 'skipped' ? 'text-yellow-600' :
                    'text-gray-400'
                }`;
            }
        }

        function updateBatchUploadProgress(current, total) {
            const percent = Math.round((current / total) * 100);
            document.getElementById('progressBarDiv').style.width = `${percent}%`;
            document.getElementById('progressTextDiv').textContent = `${percent}%`;
        }

        function updateBatchUploadStatistics() {
            const stats = calculateBatchUploadStatistics();
            document.getElementById('totalCountDiv').textContent = batchUploadTasks.length;
            document.getElementById('successCountDiv').textContent = stats.success;
            document.getElementById('errorCountDiv').textContent = stats.error;
            document.getElementById('skipCountDiv').textContent = stats.skipped;
        }

        function calculateBatchUploadStatistics() {
            // 简化版统计计算
            return {
                success: 0,
                error: 0,
                skipped: 0
            };
        }

        function switchBatchUploadTab(tabName) {
            batchUploadActiveTab = tabName;

            const allTab = document.getElementById('allTasksTabBtn');
            const completedTab = document.getElementById('completedTasksTabBtn');
            const allContent = document.getElementById('allTasksContentDiv');
            const completedContent = document.getElementById('completedTasksContentDiv');

            // 重置标签页样式
            [allTab, completedTab].forEach(tab => {
                tab.classList.remove('border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            // 隐藏所有内容
            [allContent, completedContent].forEach(content => {
                content.classList.add('hidden');
            });

            // 激活选中的标签页
            if (tabName === 'all') {
                allTab.classList.remove('border-transparent', 'text-gray-500');
                allTab.classList.add('border-blue-500', 'text-blue-600');
                allContent.classList.remove('hidden');
            } else {
                completedTab.classList.remove('border-transparent', 'text-gray-500');
                completedTab.classList.add('border-blue-500', 'text-blue-600');
                completedContent.classList.remove('hidden');
            }
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
    </script>

</body>
</html>
