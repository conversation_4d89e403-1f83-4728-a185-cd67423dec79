
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by D:\Program Files\Python37\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\Program Files\Python37\lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named 'org.python' - imported by pickle (optional), xml.sax (delayed, conditional)
missing module named org - imported by copy (optional)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), pkg_resources._vendor.packaging._manylinux (delayed, optional)
missing module named jnius - imported by pkg_resources._vendor.platformdirs.android (delayed, optional)
missing module named 'pkg_resources.extern.importlib_resources' - imported by pkg_resources._vendor.jaraco.text (optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional)
missing module named 'pkg_resources.extern.more_itertools' - imported by pkg_resources._vendor.jaraco.functools (top-level)
missing module named pkg_resources.extern.packaging - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named pkg_resources.extern.platformdirs - imported by pkg_resources.extern (top-level), pkg_resources (top-level)
missing module named 'pkg_resources.extern.jaraco' - imported by pkg_resources (top-level), pkg_resources._vendor.jaraco.text (top-level)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named vms_lib - imported by platform (delayed, conditional, optional)
missing module named java - imported by platform (delayed), unittest.mock (conditional)
missing module named _winreg - imported by platform (delayed, optional), qtpy.py3compat (conditional, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), gevent.tests.test__issue600 (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), gevent.testing.testrunner (top-level)
missing module named pwd - imported by posixpath (delayed, conditional), pathlib (delayed, conditional, optional), shutil (optional), tarfile (optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), psutil (optional), gevent.subprocess (optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by pathlib (delayed), shutil (optional), tarfile (optional), gevent.subprocess (optional), distutils.archive_util (optional), setuptools._distutils.archive_util (optional)
missing module named _aix_support - imported by setuptools._distutils.py38compat (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.register (top-level), setuptools._distutils.command.sdist (top-level)
missing module named termios - imported by getpass (optional), psutil._compat (delayed, optional), tty (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named console - imported by pyreadline.console.ansi (conditional)
missing module named startup - imported by pyreadline.keysyms.common (conditional), pyreadline.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline.keysyms.common (optional)
missing module named System - imported by pyreadline.clipboard.ironpython_clipboard (top-level), pyreadline.keysyms.ironpython_keysyms (top-level), pyreadline.console.ironpython_console (top-level), pyreadline.rlmain (conditional), webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level)
missing module named StringIO - imported by six (conditional), bottle (conditional), pyreadline.py3k_compat (conditional), qtpy.py3compat (conditional, optional), urllib3.packages.six (conditional), simplejson.compat (conditional, optional), requests_file (optional)
missing module named IronPythonConsole - imported by pyreadline.console.ironpython_console (top-level)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by gevent.thread (conditional), bottle (conditional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional), qtpy.py3compat (conditional), gevent.tests.lock_tests (optional), gevent.tests.test__core_async (optional), gevent.tests.test__monkey (delayed, optional), gevent.tests.test__monkey_futures_thread (optional), gevent.tests.test__refcount (optional), gevent.tests.test__thread (optional), gevent.tests.test__threading_2 (optional)
missing module named cStringIO - imported by cPickle (top-level), cffi.ffiplatform (optional), simplejson.compat (conditional, optional), gevent.tests.test__pywsgi (optional)
missing module named copy_reg - imported by gevent._tblib (delayed, optional), cPickle (top-level), cStringIO (top-level)
missing module named cPickle - imported by gevent._tblib (delayed, optional), bottle (conditional), pycparser.ply.yacc (delayed, optional), qtpy.py3compat (conditional, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'docutils.nodes' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.frontend' - imported by setuptools._distutils.command.check (top-level)
missing module named 'docutils.parsers' - imported by setuptools._distutils.command.check (top-level)
missing module named docutils - imported by setuptools._distutils.command.check (top-level)
missing module named unittest.IsolatedAsyncioTestCase - imported by unittest (conditional), mock.backports (conditional)
missing module named 'setuptools.extern.jaraco' - imported by setuptools._reqs (top-level), setuptools._entry_points (top-level), setuptools.command.egg_info (top-level), setuptools._vendor.jaraco.text (top-level)
missing module named setuptools.extern.importlib_resources - imported by setuptools.extern (conditional), setuptools._importlib (conditional), setuptools._vendor.jaraco.text (optional)
missing module named setuptools.extern.tomli - imported by setuptools.extern (delayed), setuptools.config.pyprojecttoml (delayed)
missing module named setuptools.extern.ordered_set - imported by setuptools.extern (top-level), setuptools.dist (top-level)
missing module named setuptools.extern.packaging - imported by setuptools.extern (top-level), setuptools.dist (top-level), setuptools._normalization (top-level), setuptools.command.egg_info (top-level), setuptools.depends (top-level)
missing module named setuptools.extern.importlib_metadata - imported by setuptools.extern (conditional), setuptools._importlib (conditional)
missing module named 'setuptools.extern.more_itertools' - imported by setuptools.dist (top-level), setuptools.config.expand (delayed), setuptools.config.pyprojecttoml (delayed), setuptools._itertools (top-level), setuptools._entry_points (top-level), setuptools.msvc (top-level), setuptools._vendor.jaraco.functools (top-level)
missing module named 'setuptools.extern.packaging.utils' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.tags' - imported by setuptools.wheel (top-level)
missing module named 'setuptools.extern.packaging.version' - imported by setuptools.config.setupcfg (top-level), setuptools.wheel (top-level)
missing module named 'importlib.metadata' - imported by setuptools._importlib (conditional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named 'setuptools.extern.packaging.specifiers' - imported by setuptools.config.setupcfg (top-level), setuptools.config._apply_pyprojecttoml (delayed)
missing module named 'setuptools.extern.packaging.requirements' - imported by setuptools.config.setupcfg (top-level), setuptools._reqs (top-level)
missing module named 'setuptools.extern.packaging.markers' - imported by setuptools.config.setupcfg (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named PyQt4 - imported by qtpy (conditional, optional), pyperclip (delayed, conditional, optional)
missing module named sip - imported by qtpy (conditional, optional), PyQt5 (top-level)
missing module named PyQt5.QtWebKitWidgets - imported by PyQt5 (optional), webview.platforms.qt (optional)
missing module named qtpy.PYSIDE6 - imported by qtpy (top-level), webview.platforms.qt (top-level)
missing module named qtpy.PYQT6 - imported by qtpy (top-level), webview.platforms.qt (top-level)
missing module named 'PySide.QtGui' - imported by qtpy.QtCore (conditional), qtpy.QtGui (conditional), qtpy.QtWidgets (conditional)
missing module named 'PyQt5.QtDataVisualization' - imported by qtpy.QtDataVisualization (conditional)
missing module named 'PySide.QtCore' - imported by qtpy (conditional, optional), qtpy.QtCore (conditional)
missing module named PySide - imported by qtpy (conditional, optional)
missing module named 'PyQt4.QtCore' - imported by qtpy (conditional, optional), qtpy.QtCore (conditional)
missing module named 'PyQt4.Qt' - imported by qtpy (conditional, optional), qtpy.QtGui (conditional, optional)
missing module named repr - imported by qtpy.py3compat (conditional)
missing module named UserDict - imported by qtpy.py3compat (conditional), simplejson.ordered_dict (top-level)
missing module named CStringIO - imported by qtpy.py3compat (conditional, optional)
missing module named ConfigParser - imported by bottle (conditional), qtpy.py3compat (conditional)
missing module named __builtin__ - imported by gevent._compat (conditional), gevent.monkey (conditional), qtpy.py3compat (conditional), gevent._ffi.loop (conditional), gevent.builtins (optional), gevent.backdoor (delayed, optional), gevent.libev.corecffi (conditional), gevent.testing.six (conditional)
missing module named AppKit - imported by webview.platforms.cocoa (top-level), pyperclip (delayed, conditional, optional), pyautogui._pyautogui_osx (top-level)
missing module named Foundation - imported by webview.platforms.cocoa (top-level), pyperclip (delayed, conditional, optional)
missing module named 'PyQt4.QtGui' - imported by qtpy.QtCore (conditional), qtpy.QtGui (conditional, optional), qtpy.QtWidgets (conditional), pyperclip (delayed, optional)
missing module named gtk - imported by pyperclip (delayed, conditional, optional)
missing module named 'Xlib.XK' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.ext' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Xlib - imported by mouseinfo (conditional), pyautogui._pyautogui_x11 (top-level)
missing module named 'Xlib.display' - imported by pyautogui._pyautogui_x11 (top-level)
missing module named Quartz - imported by pygetwindow._pygetwindow_macos (top-level), pyautogui._pyautogui_osx (optional)
missing module named Tkinter - imported by pymsgbox (conditional, optional), mouseinfo (conditional, optional)
missing module named 'rubicon.objc' - imported by mouseinfo (conditional)
missing module named rubicon - imported by mouseinfo (conditional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
excluded module named numpy - imported by openpyxl.compat.numbers (optional), PIL.Image (delayed, conditional, optional), cv2 (top-level), DrissionPage._units.screencast (delayed, optional), pyscreeze (optional), cv2.mat_wrapper (top-level), cv2.typing (top-level)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional), PIL.Image (optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named cv2.VideoWriter_fourcc - imported by cv2 (delayed, optional), DrissionPage._units.screencast (delayed, optional)
missing module named cv2.imread - imported by cv2 (delayed, optional), DrissionPage._units.screencast (delayed, optional)
missing module named cv2.VideoWriter - imported by cv2 (delayed, optional), DrissionPage._units.screencast (delayed, optional)
missing module named 'numpy.core' - imported by cv2 (optional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named iconv_codec - imported by bs4.dammit (optional)
missing module named 'html5lib.treebuilders' - imported by bs4.builder._html5lib (optional), lxml.html._html5builder (top-level), lxml.html.html5parser (top-level)
missing module named 'html5lib.constants' - imported by bs4.builder._html5lib (top-level)
missing module named html5lib - imported by bs4.builder._html5lib (top-level), lxml.html.html5parser (top-level)
missing module named urlparse - imported by bottle (conditional), lxml.html (optional), lxml.ElementInclude (optional), lxml.html.html5parser (optional), gevent.tests.test__pywsgi (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional), gevent.tests.test__example_wsgiserver (optional), gevent.tests.test__greenness (optional)
missing module named tests.KEEP_VBA - imported by tests (optional), openpyxl.reader.excel (optional)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional)
missing module named sqlite3.connect - imported by sqlite3 (top-level), DataRecorder.db_recorder (top-level)
missing module named unicodedata2 - imported by charset_normalizer.utils (optional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named six.moves.range - imported by six.moves (top-level), cryptography.hazmat.backends.openssl.backend (top-level)
runtime module named six.moves - imported by cryptography.hazmat.backends.openssl.backend (top-level)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional)
missing module named Queue - imported by gevent.queue (conditional), urllib3.util.queue (conditional)
missing module named "'urllib3.packages.six.moves.urllib'.parse" - imported by urllib3.request (top-level), urllib3.poolmanager (top-level)
runtime module named urllib3.packages.six.moves - imported by http.client (top-level), urllib3.util.response (top-level), urllib3.connectionpool (top-level), 'urllib3.packages.six.moves.urllib' (top-level), urllib3.util.queue (top-level)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named _dummy_threading - imported by dummy_threading (optional)
missing module named fcntl - imported by psutil._compat (delayed, optional), gevent.fileobject (optional), gevent.os (optional), gevent.subprocess (conditional), eventlet.greenio.base (delayed, optional), filelock (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named 'python_socks._errors' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named posix - imported by os (conditional, optional)
missing module named resource - imported by posix (top-level), test.support (optional)
missing module named WebBrowserInterop - imported by webview.platforms.mshtml (top-level)
missing module named 'Microsoft.Web' - imported by webview.platforms.edgechromium (top-level)
missing module named Microsoft - imported by webview.platforms.edgechromium (top-level)
missing module named 'System.Globalization' - imported by webview.platforms.edgechromium (top-level)
missing module named 'System.Collections' - imported by webview.platforms.edgechromium (top-level)
missing module named cefpython3 - imported by webview.platforms.cef (top-level)
missing module named 'System.Threading' - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level)
missing module named 'System.Drawing' - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level)
missing module named 'System.Windows' - imported by webview.platforms.winforms (top-level), webview.platforms.edgechromium (top-level), webview.platforms.mshtml (top-level)
missing module named Security - imported by webview.platforms.cocoa (delayed)
missing module named PyObjCTools - imported by webview.platforms.cocoa (top-level)
missing module named objc - imported by webview.platforms.cocoa (top-level)
missing module named WebKit - imported by webview.platforms.cocoa (top-level)
missing module named 'PySide.QtWebKit' - imported by qtpy.QtWebEngineWidgets (conditional)
missing module named 'PyQt4.QtWebKit' - imported by qtpy.QtWebEngineWidgets (conditional)
missing module named 'PyQt5.QtWebKit' - imported by qtpy.QtWebEngineWidgets (conditional, optional)
missing module named 'PyQt5.QtWebKitWidgets' - imported by qtpy.QtWebEngineWidgets (conditional, optional)
missing module named 'PyQt5.QtWebEngineWidgets' - imported by qtpy.QtWebEngineWidgets (conditional, optional)
missing module named 'PySide.QtNetwork' - imported by qtpy.QtNetwork (conditional)
missing module named 'PyQt4.QtNetwork' - imported by qtpy.QtNetwork (conditional)
missing module named 'gi.repository' - imported by webview.platforms.gtk (top-level)
missing module named gi - imported by webview.platforms.gtk (top-level)
missing module named Cheetah - imported by bottle (delayed)
missing module named 'mako.lookup' - imported by bottle (delayed)
missing module named mako - imported by bottle (delayed)
missing module named uvloop - imported by bottle (delayed)
missing module named aiohttp_wsgi - imported by bottle (delayed)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named bjoern - imported by bottle (delayed)
missing module named monotonic - imported by eventlet.hubs.hub (optional), eventlet (optional)
missing module named itimer - imported by eventlet.hubs.hub (conditional, optional)
missing module named gunicorn - imported by bottle (delayed)
missing module named mimetools - imported by gevent.pywsgi (optional)
missing module named zope.schema - imported by zope (optional), gevent._interfaces (optional)
missing module named 'backports.socketpair' - imported by gevent._socketcommon (optional)
missing module named __pypy__ - imported by gevent._tblib (delayed, optional)
missing module named _continuation - imported by gevent.greenlet (conditional)
missing module named selectors2 - imported by gevent.selectors (optional), gevent.tests.test__monkey_selectors (optional)
missing module named _import_wait - imported by gevent.tests.test__import_wait (top-level)
missing module named _blocks_at_top_level - imported by gevent.tests.test__import_blocking_in_greenlet (delayed, optional)
missing module named SimpleHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named BaseHTTPServer - imported by gevent.tests.test__greenness (optional)
missing module named getaddrinfo_module - imported by gevent.tests.test__getaddrinfo_import (top-level)
missing module named 'gevent.resolver.cares' - imported by gevent.ares (top-level), gevent.tests.test__ares_host_result (optional)
missing module named objgraph - imported by gevent.testing.leakcheck (optional)
missing module named cares - imported by gevent.resolver.ares (top-level)
missing module named _setuputils - imported by gevent.libev._corecffi_build (optional), gevent.libuv._corecffi_build (optional)
missing module named gevent.libev._corecffi - imported by gevent.libev (top-level), gevent.libev.corecffi (top-level), gevent.libev.watcher (top-level)
missing module named _setuplibev - imported by gevent.libev._corecffi_build (optional)
missing module named diesel - imported by bottle (delayed)
missing module named 'twisted.internet' - imported by bottle (delayed)
missing module named 'twisted.python' - imported by bottle (delayed)
missing module named twisted - imported by bottle (delayed)
missing module named 'google.appengine' - imported by bottle (delayed)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named fapws - imported by bottle (delayed)
missing module named meinheld - imported by bottle (delayed)
missing module named 'paste.translogger' - imported by bottle (delayed)
missing module named paste - imported by bottle (delayed)
missing module named waitress - imported by bottle (delayed)
missing module named 'cheroot.ssl' - imported by bottle (delayed)
missing module named cheroot - imported by bottle (delayed)
missing module named cherrypy - imported by bottle (delayed)
missing module named flup - imported by bottle (delayed)
missing module named Cookie - imported by bottle (conditional)
missing module named httplib - imported by bottle (conditional), gevent.tests.test__socket_ssl (optional)
missing module named ujson - imported by bottle (optional)
missing module named 'wsgiref.types' - imported by webview.http (conditional), tornado.wsgi (conditional)
missing module named _uuid - imported by uuid (optional)
