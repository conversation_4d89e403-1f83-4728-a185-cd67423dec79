# 发票申请查询系统使用说明

## 🎯 系统概述

这是一个基于PyWebView的桌面应用程序，专门用于天猫供应商发票申请的查询和上传操作。

## 🚀 快速开始

### 1. 获取Cookie

#### 方式一：自动登录（推荐）
1. **点击登录按钮**：点击绿色的"自动登录"按钮
2. **浏览器自动打开**：系统会自动打开浏览器并跳转到天猫登录页面
3. **完成登录**：在浏览器中输入用户名密码完成登录
4. **自动监控**：系统每5秒检查一次登录状态，无需手动操作
5. **自动获取Cookie**：登录成功后系统自动获取Cookie并保存到配置文件
6. **浏览器自动关闭**：Cookie获取完成后浏览器会自动关闭
7. **成功提示**：软件界面显示"登录成功！Cookie已自动获取并保存，浏览器已关闭"
8. **自动查询**：系统自动开始查询发票数据

**注意事项**：
- 整个过程最多监控10分钟，超时会自动停止
- 如果登录页面长时间未跳转，请检查网络连接
- 登录成功的标志是页面跳转到发票申请页面

#### 方式二：手动获取Cookie
- 打开浏览器，登录天猫供应商后台
- 进入发票申请页面
- 按F12打开开发者工具，切换到Network标签页
- 刷新页面，找到`pageQueryInvoiceApply`请求
- 复制请求头中的完整Cookie字符串
- 粘贴到应用的Cookie文本框中，点击"设置Cookie"

### 2. 上传发票
点击紫色的"上传发票"按钮：
- 系统自动打开发票申请页面
- 自动点击发票录入按钮
- 自动点击上传PDF按钮
- 文件选择对话框自动打开
- 选择PDF文件完成上传

### 3. 查询数据
点击"查询数据"按钮：
- 查看所有发票申请记录
- 支持分页浏览
- 可以导出Excel文件

## 💡 功能特点

### 🤖 自动化上传
- **自动上传**：一键完成发票上传流程
- **智能检测**：自动检测页面元素和状态
- **文件处理**：自动打开文件选择对话框

### 🎨 简洁界面
- **主要功能**：
  - 📤 上传发票（紫色）- 上传PDF文件
  - 📊 查询数据（蓝色）- 查看发票记录
  - 🔐 Cookie管理 - 手动设置和管理Cookie

### 🔧 智能处理
- **Cookie管理**：自动保存和加载Cookie
- **错误处理**：完善的异常处理和用户提示
- **状态反馈**：实时显示操作进度和结果

## ⚠️ 注意事项

### 1. 首次使用
- 必须先手动获取Cookie并设置
- 确保网络连接稳定
- 准备好天猫供应商账号和密码

### 2. 文件要求
- 上传文件必须是PDF格式
- 文件大小不要超过系统限制
- 确保文件内容清晰可读

### 3. 系统要求
- 需要安装Chrome浏览器
- 需要安装DrissionPage库
- Windows系统推荐

## 🔄 操作流程

```
启动应用
    ↓
手动获取Cookie
    ↓
设置Cookie到应用中
    ↓
点击"上传发票"
    ↓
选择PDF文件
    ↓
完成上传
    ↓
点击"查询数据"查看结果
```

## 🛠️ 故障排除

### 1. Cookie设置失败
- 检查Cookie格式是否正确
- 确认Cookie是否完整
- 重新从浏览器复制Cookie

### 2. 上传失败
- 确认Cookie是否有效
- 检查PDF文件是否损坏
- 重新获取Cookie后再试

### 3. 页面无法打开
- 检查网络连接
- 确认天猫供应商网站是否正常
- 重启应用程序

## 📞 技术支持

如果遇到问题，请检查：
1. 网络连接是否正常
2. Cookie是否已过期
3. 文件格式是否正确
4. 浏览器是否正常工作

## 🎉 使用技巧

### 1. 提高效率
- 定期检查Cookie有效性
- 批量准备PDF文件
- 使用快捷键操作

### 2. 避免错误
- 确保文件命名规范
- 检查文件内容完整性
- 及时更新Cookie

### 3. 最佳实践
- 在网络稳定时使用
- 关闭不必要的浏览器窗口
- 定期清理临时文件

通过这个简化的系统，您可以高效地完成发票申请的查询和上传工作！
