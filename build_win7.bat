@echo off
chcp 65001 >nul
echo ========================================
echo Windows 7 兼容版本构建脚本
echo ========================================
echo.

echo 1. 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "发票申请查询系统_Win7.exe" del "发票申请查询系统_Win7.exe"
echo    ✅ 清理完成

echo.
echo 2. 检查依赖...
python -c "import openpyxl; print('✅ openpyxl 可用')" 2>nul || (echo "❌ openpyxl 未安装" && pause && exit /b 1)
python -c "import webview; print('✅ webview 可用')" 2>nul || (echo "❌ webview 未安装" && pause && exit /b 1)
python -c "import requests; print('✅ requests 可用')" 2>nul || (echo "❌ requests 未安装" && pause && exit /b 1)
python -c "from dateutil.relativedelta import relativedelta; print('✅ dateutil 可用')" 2>nul || (echo "❌ dateutil 未安装" && pause && exit /b 1)
echo    ✅ 依赖检查完成

echo.
echo 3. 开始构建 Windows 7 兼容版本...
pyinstaller build_win7.spec

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo ✅ 构建成功！
    echo ========================================
    echo.
    echo 输出文件: dist\发票申请查询系统_Win7.exe
    echo.
    echo 🎯 Windows 7 优化特性:
    echo    - 移除了pandas依赖，避免DLL问题
    echo    - 使用openpyxl替代pandas进行Excel操作
    echo    - 优化了依赖包，减少兼容性问题
    echo.
    echo 📋 测试建议:
    echo    1. 在Windows 7系统上测试Excel导出功能
    echo    2. 验证Chrome路径检测功能
    echo    3. 确认日期计算修复生效
    echo.
    if exist "dist\发票申请查询系统_Win7.exe" (
        echo 文件大小: 
        for %%I in ("dist\发票申请查询系统_Win7.exe") do echo    %%~zI 字节
    )
    echo.
    pause
) else (
    echo.
    echo ========================================
    echo ❌ 构建失败！
    echo ========================================
    echo.
    echo 请检查错误信息并重试
    pause
    exit /b 1
)
