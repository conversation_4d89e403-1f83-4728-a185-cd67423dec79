('D:\\py-ide\\naide-fapiao\\build\\发票申请查询系统\\PYZ-00.pyz',
 [('DataRecorder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\__init__.py',
   'PYMODULE'),
  ('DataRecorder.base',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\base.py',
   'PYMODULE'),
  ('DataRecorder.byte_recorder',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DataRecorder\\byte_recorder.py',
   'PYMODULE'),
  ('DataRecorder.db_recorder',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DataRecorder\\db_recorder.py',
   'PYMODULE'),
  ('DataRecorder.filler',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\filler.py',
   'PYMODULE'),
  ('DataRecorder.recorder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\recorder.py',
   'PYMODULE'),
  ('DataRecorder.setter',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\setter.py',
   'PYMODULE'),
  ('DataRecorder.style',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DataRecorder\\style\\__init__.py',
   'PYMODULE'),
  ('DataRecorder.style.cell_style',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DataRecorder\\style\\cell_style.py',
   'PYMODULE'),
  ('DataRecorder.tools',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DataRecorder\\tools.py',
   'PYMODULE'),
  ('DownloadKit',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DownloadKit\\__init__.py',
   'PYMODULE'),
  ('DownloadKit._funcs',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DownloadKit\\_funcs.py',
   'PYMODULE'),
  ('DownloadKit.downloadKit',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DownloadKit\\downloadKit.py',
   'PYMODULE'),
  ('DownloadKit.mission',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DownloadKit\\mission.py',
   'PYMODULE'),
  ('DownloadKit.setter',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DownloadKit\\setter.py',
   'PYMODULE'),
  ('DrissionPage',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DrissionPage\\__init__.py',
   'PYMODULE'),
  ('DrissionPage._base', '-', 'PYMODULE'),
  ('DrissionPage._base.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_base\\base.py',
   'PYMODULE'),
  ('DrissionPage._base.chromium',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_base\\chromium.py',
   'PYMODULE'),
  ('DrissionPage._base.driver',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_base\\driver.py',
   'PYMODULE'),
  ('DrissionPage._configs', '-', 'PYMODULE'),
  ('DrissionPage._configs.chromium_options',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_configs\\chromium_options.py',
   'PYMODULE'),
  ('DrissionPage._configs.options_manage',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_configs\\options_manage.py',
   'PYMODULE'),
  ('DrissionPage._configs.session_options',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_configs\\session_options.py',
   'PYMODULE'),
  ('DrissionPage._elements', '-', 'PYMODULE'),
  ('DrissionPage._elements.chromium_element',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_elements\\chromium_element.py',
   'PYMODULE'),
  ('DrissionPage._elements.none_element',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_elements\\none_element.py',
   'PYMODULE'),
  ('DrissionPage._elements.session_element',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_elements\\session_element.py',
   'PYMODULE'),
  ('DrissionPage._functions', '-', 'PYMODULE'),
  ('DrissionPage._functions.browser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\browser.py',
   'PYMODULE'),
  ('DrissionPage._functions.by',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\by.py',
   'PYMODULE'),
  ('DrissionPage._functions.cookies',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\cookies.py',
   'PYMODULE'),
  ('DrissionPage._functions.elements',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\elements.py',
   'PYMODULE'),
  ('DrissionPage._functions.keys',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\keys.py',
   'PYMODULE'),
  ('DrissionPage._functions.locator',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\locator.py',
   'PYMODULE'),
  ('DrissionPage._functions.settings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\settings.py',
   'PYMODULE'),
  ('DrissionPage._functions.texts',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\texts.py',
   'PYMODULE'),
  ('DrissionPage._functions.tools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\tools.py',
   'PYMODULE'),
  ('DrissionPage._functions.web',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_functions\\web.py',
   'PYMODULE'),
  ('DrissionPage._pages', '-', 'PYMODULE'),
  ('DrissionPage._pages.chromium_base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\chromium_base.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_frame',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\chromium_frame.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_page',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\chromium_page.py',
   'PYMODULE'),
  ('DrissionPage._pages.chromium_tab',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\chromium_tab.py',
   'PYMODULE'),
  ('DrissionPage._pages.mix_tab',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\mix_tab.py',
   'PYMODULE'),
  ('DrissionPage._pages.session_page',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\session_page.py',
   'PYMODULE'),
  ('DrissionPage._pages.web_page',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_pages\\web_page.py',
   'PYMODULE'),
  ('DrissionPage._units', '-', 'PYMODULE'),
  ('DrissionPage._units.actions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\actions.py',
   'PYMODULE'),
  ('DrissionPage._units.clicker',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\clicker.py',
   'PYMODULE'),
  ('DrissionPage._units.console',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\console.py',
   'PYMODULE'),
  ('DrissionPage._units.cookies_setter',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\cookies_setter.py',
   'PYMODULE'),
  ('DrissionPage._units.downloader',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\downloader.py',
   'PYMODULE'),
  ('DrissionPage._units.listener',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\listener.py',
   'PYMODULE'),
  ('DrissionPage._units.rect',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\rect.py',
   'PYMODULE'),
  ('DrissionPage._units.screencast',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\screencast.py',
   'PYMODULE'),
  ('DrissionPage._units.scroller',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\scroller.py',
   'PYMODULE'),
  ('DrissionPage._units.selector',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\selector.py',
   'PYMODULE'),
  ('DrissionPage._units.setter',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\setter.py',
   'PYMODULE'),
  ('DrissionPage._units.states',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\states.py',
   'PYMODULE'),
  ('DrissionPage._units.waiter',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\DrissionPage\\_units\\waiter.py',
   'PYMODULE'),
  ('DrissionPage.errors',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DrissionPage\\errors.py',
   'PYMODULE'),
  ('DrissionPage.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\DrissionPage\\version.py',
   'PYMODULE'),
  ('OpenSSL',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL.tsafe',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\tsafe.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('PIL',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('PySide2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\PySide2\\__init__.py',
   'PYMODULE'),
  ('__future__', 'D:\\Program Files\\Python37\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\Python37\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\Python37\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_dummy_thread',
   'D:\\Program Files\\Python37\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\Program Files\\Python37\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Program Files\\Python37\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\Python37\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\Program Files\\Python37\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyio', 'D:\\Program Files\\Python37\\lib\\_pyio.py', 'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Program Files\\Python37\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\Python37\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\Python37\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Program Files\\Python37\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Program Files\\Python37\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Program Files\\Python37\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files\\Python37\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files\\Python37\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files\\Python37\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files\\Python37\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files\\Python37\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files\\Python37\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files\\Python37\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files\\Python37\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files\\Python37\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files\\Python37\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files\\Python37\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files\\Python37\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files\\Python37\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files\\Python37\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files\\Python37\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files\\Python37\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files\\Python37\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files\\Python37\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files\\Python37\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files\\Python37\\lib\\base64.py', 'PYMODULE'),
  ('bcrypt',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('bcrypt.__about__',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bcrypt\\__about__.py',
   'PYMODULE'),
  ('bdb', 'D:\\Program Files\\Python37\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Program Files\\Python37\\lib\\bisect.py', 'PYMODULE'),
  ('bottle',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bottle.py',
   'PYMODULE'),
  ('brotli',
   'D:\\Program Files\\Python37\\lib\\site-packages\\brotli.py',
   'PYMODULE'),
  ('bs4',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.builder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.dammit',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\Program Files\\Python37\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2', 'D:\\Program Files\\Python37\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Program Files\\Python37\\lib\\calendar.py', 'PYMODULE'),
  ('cchardet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cchardet\\__init__.py',
   'PYMODULE'),
  ('cchardet.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cchardet\\version.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Program Files\\Python37\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'D:\\Program Files\\Python37\\lib\\cgi.py', 'PYMODULE'),
  ('chardet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.enums',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.escprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.assets',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\assets\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program Files\\Python37\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.md',
   'D:\\Program Files\\Python37\\lib\\site-packages\\charset_normalizer\\md.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('clr',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr.py',
   'PYMODULE'),
  ('clr_loader',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\__init__.py',
   'PYMODULE'),
  ('clr_loader.ffi.hostfxr',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.ffi.mono',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\mono.py',
   'PYMODULE'),
  ('clr_loader.ffi.netfx',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\ffi\\netfx.py',
   'PYMODULE'),
  ('clr_loader.hostfxr',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\hostfxr.py',
   'PYMODULE'),
  ('clr_loader.mono',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\mono.py',
   'PYMODULE'),
  ('clr_loader.netfx',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\netfx.py',
   'PYMODULE'),
  ('clr_loader.types',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\types.py',
   'PYMODULE'),
  ('clr_loader.util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\util\\__init__.py',
   'PYMODULE'),
  ('clr_loader.util.clr_error',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\util\\clr_error.py',
   'PYMODULE'),
  ('clr_loader.util.coreclr_errors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\util\\coreclr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.find',
   'D:\\Program Files\\Python37\\lib\\site-packages\\clr_loader\\util\\find.py',
   'PYMODULE'),
  ('clr_loader.util.hostfxr_errors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\util\\hostfxr_errors.py',
   'PYMODULE'),
  ('clr_loader.util.runtime_spec',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\clr_loader\\util\\runtime_spec.py',
   'PYMODULE'),
  ('cmd', 'D:\\Program Files\\Python37\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Program Files\\Python37\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files\\Python37\\lib\\codeop.py', 'PYMODULE'),
  ('colorama',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'D:\\Program Files\\Python37\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Program Files\\Python37\\lib\\colorsys.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Program Files\\Python37\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files\\Python37\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files\\Python37\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Program Files\\Python37\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\Python37\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\Python37\\lib\\copy.py', 'PYMODULE'),
  ('cryptography',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat._der',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\_der.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.interfaces',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\interfaces.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dh',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.dsa',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed25519',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ed448',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.encode_asn1',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\encode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hashes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.hmac',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\hmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ocsp',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ocsp.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.poly1305',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\poly1305.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x25519',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x448',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.x509',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\x509.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.kdf.scrypt',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\kdf\\scrypt.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs7',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs7.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('cryptography.utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.x509',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.ocsp',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\ocsp.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\Program Files\\Python37\\lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files\\Python37\\lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\Python37\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\Python37\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\Python37\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\Python37\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\Python37\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files\\Python37\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('curses',
   'D:\\Program Files\\Python37\\lib\\curses\\__init__.py',
   'PYMODULE'),
  ('curses.has_key',
   'D:\\Program Files\\Python37\\lib\\curses\\has_key.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\Python37\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Program Files\\Python37\\lib\\datetime.py', 'PYMODULE'),
  ('dateutil',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files\\Python37\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files\\Python37\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\Python37\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'D:\\Program Files\\Python37\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Program Files\\Python37\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Program Files\\Python37\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Program Files\\Python37\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Program Files\\Python37\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Program Files\\Python37\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Program Files\\Python37\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Program Files\\Python37\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Program Files\\Python37\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Program Files\\Python37\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Program Files\\Python37\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Program Files\\Python37\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Program Files\\Python37\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Program Files\\Python37\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Program Files\\Python37\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Program Files\\Python37\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Program Files\\Python37\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Program Files\\Python37\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Program Files\\Python37\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Program Files\\Python37\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('dns',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dns\\__init__.py',
   'PYMODULE'),
  ('dns.rdtypes',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dns\\rdtypes\\__init__.py',
   'PYMODULE'),
  ('dns.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\dns\\version.py',
   'PYMODULE'),
  ('doctest', 'D:\\Program Files\\Python37\\lib\\doctest.py', 'PYMODULE'),
  ('dummy_threading',
   'D:\\Program Files\\Python37\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email', 'D:\\Program Files\\Python37\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\Python37\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\Python37\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\Python37\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\Python37\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\Python37\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\Python37\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\Python37\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\Python37\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\Python37\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\Python37\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\Python37\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\Python37\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\Python37\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\Python37\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\Python37\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\Python37\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\Python37\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\Python37\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\Python37\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('eventlet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\__init__.py',
   'PYMODULE'),
  ('eventlet._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\_version.py',
   'PYMODULE'),
  ('eventlet.convenience',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\convenience.py',
   'PYMODULE'),
  ('eventlet.corolocal',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\corolocal.py',
   'PYMODULE'),
  ('eventlet.event',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\event.py',
   'PYMODULE'),
  ('eventlet.green',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.BaseHTTPServer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\BaseHTTPServer.py',
   'PYMODULE'),
  ('eventlet.green.MySQLdb',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\MySQLdb.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.SSL',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.crypto',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.tsafe',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\OpenSSL\\tsafe.py',
   'PYMODULE'),
  ('eventlet.green.OpenSSL.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\OpenSSL\\version.py',
   'PYMODULE'),
  ('eventlet.green.Queue',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\green\\Queue.py',
   'PYMODULE'),
  ('eventlet.green.SocketServer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\SocketServer.py',
   'PYMODULE'),
  ('eventlet.green._socket_nodns',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\_socket_nodns.py',
   'PYMODULE'),
  ('eventlet.green.builtin',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\builtin.py',
   'PYMODULE'),
  ('eventlet.green.os',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\green\\os.py',
   'PYMODULE'),
  ('eventlet.green.select',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\select.py',
   'PYMODULE'),
  ('eventlet.green.selectors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\selectors.py',
   'PYMODULE'),
  ('eventlet.green.socket',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\socket.py',
   'PYMODULE'),
  ('eventlet.green.ssl',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\green\\ssl.py',
   'PYMODULE'),
  ('eventlet.green.subprocess',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\subprocess.py',
   'PYMODULE'),
  ('eventlet.green.thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\thread.py',
   'PYMODULE'),
  ('eventlet.green.threading',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\green\\threading.py',
   'PYMODULE'),
  ('eventlet.green.time',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\green\\time.py',
   'PYMODULE'),
  ('eventlet.greenio',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\greenio\\__init__.py',
   'PYMODULE'),
  ('eventlet.greenio.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\greenio\\base.py',
   'PYMODULE'),
  ('eventlet.greenio.py3',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\greenio\\py3.py',
   'PYMODULE'),
  ('eventlet.greenpool',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\greenpool.py',
   'PYMODULE'),
  ('eventlet.greenthread',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\greenthread.py',
   'PYMODULE'),
  ('eventlet.hubs',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\hubs\\__init__.py',
   'PYMODULE'),
  ('eventlet.hubs.asyncio',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\hubs\\asyncio.py',
   'PYMODULE'),
  ('eventlet.hubs.hub',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\hubs\\hub.py',
   'PYMODULE'),
  ('eventlet.hubs.timer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\hubs\\timer.py',
   'PYMODULE'),
  ('eventlet.lock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\lock.py',
   'PYMODULE'),
  ('eventlet.patcher',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\patcher.py',
   'PYMODULE'),
  ('eventlet.queue',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\queue.py',
   'PYMODULE'),
  ('eventlet.semaphore',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\semaphore.py',
   'PYMODULE'),
  ('eventlet.support',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\support\\__init__.py',
   'PYMODULE'),
  ('eventlet.support.greendns',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\support\\greendns.py',
   'PYMODULE'),
  ('eventlet.support.greenlets',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\support\\greenlets.py',
   'PYMODULE'),
  ('eventlet.support.psycopg2_patcher',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\eventlet\\support\\psycopg2_patcher.py',
   'PYMODULE'),
  ('eventlet.timeout',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\timeout.py',
   'PYMODULE'),
  ('eventlet.tpool',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\tpool.py',
   'PYMODULE'),
  ('eventlet.wsgi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\eventlet\\wsgi.py',
   'PYMODULE'),
  ('filelock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\filelock.py',
   'PYMODULE'),
  ('fractions', 'D:\\Program Files\\Python37\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\Python37\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files\\Python37\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files\\Python37\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\Python37\\lib\\gettext.py', 'PYMODULE'),
  ('gevent',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\__init__.py',
   'PYMODULE'),
  ('gevent._abstract_linkable',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\_abstract_linkable.py',
   'PYMODULE'),
  ('gevent._compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_compat.py',
   'PYMODULE'),
  ('gevent._config',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_config.py',
   'PYMODULE'),
  ('gevent._ffi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ffi\\__init__.py',
   'PYMODULE'),
  ('gevent._ffi.callback',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ffi\\callback.py',
   'PYMODULE'),
  ('gevent._ffi.loop',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ffi\\loop.py',
   'PYMODULE'),
  ('gevent._ffi.watcher',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ffi\\watcher.py',
   'PYMODULE'),
  ('gevent._fileobjectcommon',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\_fileobjectcommon.py',
   'PYMODULE'),
  ('gevent._fileobjectposix',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\_fileobjectposix.py',
   'PYMODULE'),
  ('gevent._greenlet_primitives',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\_greenlet_primitives.py',
   'PYMODULE'),
  ('gevent._hub_local',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_hub_local.py',
   'PYMODULE'),
  ('gevent._hub_primitives',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\_hub_primitives.py',
   'PYMODULE'),
  ('gevent._ident',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ident.py',
   'PYMODULE'),
  ('gevent._imap',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_imap.py',
   'PYMODULE'),
  ('gevent._interfaces',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_interfaces.py',
   'PYMODULE'),
  ('gevent._monitor',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_monitor.py',
   'PYMODULE'),
  ('gevent._patcher',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_patcher.py',
   'PYMODULE'),
  ('gevent._semaphore',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_semaphore.py',
   'PYMODULE'),
  ('gevent._socket2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_socket2.py',
   'PYMODULE'),
  ('gevent._socket3',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_socket3.py',
   'PYMODULE'),
  ('gevent._socketcommon',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_socketcommon.py',
   'PYMODULE'),
  ('gevent._ssl2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ssl2.py',
   'PYMODULE'),
  ('gevent._ssl3',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_ssl3.py',
   'PYMODULE'),
  ('gevent._sslgte279',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_sslgte279.py',
   'PYMODULE'),
  ('gevent._tblib',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_tblib.py',
   'PYMODULE'),
  ('gevent._threading',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_threading.py',
   'PYMODULE'),
  ('gevent._tracer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_tracer.py',
   'PYMODULE'),
  ('gevent._util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_util.py',
   'PYMODULE'),
  ('gevent._util_py2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_util_py2.py',
   'PYMODULE'),
  ('gevent._waiter',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\_waiter.py',
   'PYMODULE'),
  ('gevent.ares',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\ares.py',
   'PYMODULE'),
  ('gevent.backdoor',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\backdoor.py',
   'PYMODULE'),
  ('gevent.baseserver',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\baseserver.py',
   'PYMODULE'),
  ('gevent.builtins',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\builtins.py',
   'PYMODULE'),
  ('gevent.contextvars',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\contextvars.py',
   'PYMODULE'),
  ('gevent.core',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\core.py',
   'PYMODULE'),
  ('gevent.event',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\event.py',
   'PYMODULE'),
  ('gevent.events',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\events.py',
   'PYMODULE'),
  ('gevent.exceptions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\exceptions.py',
   'PYMODULE'),
  ('gevent.fileobject',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\fileobject.py',
   'PYMODULE'),
  ('gevent.greenlet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\greenlet.py',
   'PYMODULE'),
  ('gevent.hub',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\hub.py',
   'PYMODULE'),
  ('gevent.libev',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\libev\\__init__.py',
   'PYMODULE'),
  ('gevent.libev._corecffi_build',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\libev\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libev.corecffi',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\libev\\corecffi.py',
   'PYMODULE'),
  ('gevent.libev.watcher',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\libev\\watcher.py',
   'PYMODULE'),
  ('gevent.libuv',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\libuv\\__init__.py',
   'PYMODULE'),
  ('gevent.libuv._corecffi_build',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\libuv\\_corecffi_build.py',
   'PYMODULE'),
  ('gevent.libuv.loop',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\libuv\\loop.py',
   'PYMODULE'),
  ('gevent.libuv.watcher',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\libuv\\watcher.py',
   'PYMODULE'),
  ('gevent.local',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\local.py',
   'PYMODULE'),
  ('gevent.lock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\lock.py',
   'PYMODULE'),
  ('gevent.monkey',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\monkey.py',
   'PYMODULE'),
  ('gevent.os',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\os.py',
   'PYMODULE'),
  ('gevent.pool',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\pool.py',
   'PYMODULE'),
  ('gevent.pywsgi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\pywsgi.py',
   'PYMODULE'),
  ('gevent.queue',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\queue.py',
   'PYMODULE'),
  ('gevent.resolver',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\__init__.py',
   'PYMODULE'),
  ('gevent.resolver._addresses',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\_addresses.py',
   'PYMODULE'),
  ('gevent.resolver._hostsfile',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\_hostsfile.py',
   'PYMODULE'),
  ('gevent.resolver.ares',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\resolver\\ares.py',
   'PYMODULE'),
  ('gevent.resolver.blocking',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\blocking.py',
   'PYMODULE'),
  ('gevent.resolver.dnspython',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\dnspython.py',
   'PYMODULE'),
  ('gevent.resolver.thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver\\thread.py',
   'PYMODULE'),
  ('gevent.resolver_ares',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\resolver_ares.py',
   'PYMODULE'),
  ('gevent.resolver_thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\resolver_thread.py',
   'PYMODULE'),
  ('gevent.select',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\select.py',
   'PYMODULE'),
  ('gevent.selectors',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\selectors.py',
   'PYMODULE'),
  ('gevent.server',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\server.py',
   'PYMODULE'),
  ('gevent.signal',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\signal.py',
   'PYMODULE'),
  ('gevent.socket',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\socket.py',
   'PYMODULE'),
  ('gevent.ssl',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\ssl.py',
   'PYMODULE'),
  ('gevent.subprocess',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\subprocess.py',
   'PYMODULE'),
  ('gevent.testing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\__init__.py',
   'PYMODULE'),
  ('gevent.testing.errorhandler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\errorhandler.py',
   'PYMODULE'),
  ('gevent.testing.exception',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\exception.py',
   'PYMODULE'),
  ('gevent.testing.flaky',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\testing\\flaky.py',
   'PYMODULE'),
  ('gevent.testing.hub',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\testing\\hub.py',
   'PYMODULE'),
  ('gevent.testing.leakcheck',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\leakcheck.py',
   'PYMODULE'),
  ('gevent.testing.modules',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\modules.py',
   'PYMODULE'),
  ('gevent.testing.monkey_test',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\monkey_test.py',
   'PYMODULE'),
  ('gevent.testing.openfiles',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\openfiles.py',
   'PYMODULE'),
  ('gevent.testing.params',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\params.py',
   'PYMODULE'),
  ('gevent.testing.patched_tests_setup',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\patched_tests_setup.py',
   'PYMODULE'),
  ('gevent.testing.resources',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\resources.py',
   'PYMODULE'),
  ('gevent.testing.six',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\testing\\six.py',
   'PYMODULE'),
  ('gevent.testing.skipping',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\skipping.py',
   'PYMODULE'),
  ('gevent.testing.sockets',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\sockets.py',
   'PYMODULE'),
  ('gevent.testing.support',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\support.py',
   'PYMODULE'),
  ('gevent.testing.switching',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\switching.py',
   'PYMODULE'),
  ('gevent.testing.sysinfo',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\sysinfo.py',
   'PYMODULE'),
  ('gevent.testing.testcase',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\testcase.py',
   'PYMODULE'),
  ('gevent.testing.testrunner',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\testrunner.py',
   'PYMODULE'),
  ('gevent.testing.timing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\timing.py',
   'PYMODULE'),
  ('gevent.testing.travis',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\testing\\travis.py',
   'PYMODULE'),
  ('gevent.testing.util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\testing\\util.py',
   'PYMODULE'),
  ('gevent.tests',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.__main__',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\__main__.py',
   'PYMODULE'),
  ('gevent.tests._blocks_at_top_level',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_blocks_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._import_import_patch',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_import_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_patch',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_import_patch.py',
   'PYMODULE'),
  ('gevent.tests._import_wait',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_import_wait.py',
   'PYMODULE'),
  ('gevent.tests._imports_at_top_level',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests._imports_imports_at_top_level',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\_imports_imports_at_top_level.py',
   'PYMODULE'),
  ('gevent.tests.getaddrinfo_module',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\getaddrinfo_module.py',
   'PYMODULE'),
  ('gevent.tests.known_failures',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\known_failures.py',
   'PYMODULE'),
  ('gevent.tests.lock_tests',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\lock_tests.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\__init__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.__main__',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\__main__.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_no_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue1526_with_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\issue1526_with_monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.issue302monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\issue302monkey.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.script',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\script.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_monkey_patches',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_monkey_patches.py',
   'PYMODULE'),
  ('gevent.tests.monkey_package.threadpool_no_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\monkey_package\\threadpool_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__GreenletExit',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__GreenletExit.py',
   'PYMODULE'),
  ('gevent.tests.test___config',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test___config.py',
   'PYMODULE'),
  ('gevent.tests.test___ident',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test___ident.py',
   'PYMODULE'),
  ('gevent.tests.test___monitor',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test___monitor.py',
   'PYMODULE'),
  ('gevent.tests.test___monkey_patching',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test___monkey_patching.py',
   'PYMODULE'),
  ('gevent.tests.test__all__',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__all__.py',
   'PYMODULE'),
  ('gevent.tests.test__api',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__api.py',
   'PYMODULE'),
  ('gevent.tests.test__api_timeout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__api_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_host_result',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__ares_host_result.py',
   'PYMODULE'),
  ('gevent.tests.test__ares_timeout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__ares_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__backdoor',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__backdoor.py',
   'PYMODULE'),
  ('gevent.tests.test__close_backend_fd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__close_backend_fd.py',
   'PYMODULE'),
  ('gevent.tests.test__compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__compat.py',
   'PYMODULE'),
  ('gevent.tests.test__contextvars',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__contextvars.py',
   'PYMODULE'),
  ('gevent.tests.test__core',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core.py',
   'PYMODULE'),
  ('gevent.tests.test__core_async',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_async.py',
   'PYMODULE'),
  ('gevent.tests.test__core_callback',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__core_fork',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_fork.py',
   'PYMODULE'),
  ('gevent.tests.test__core_loop_run',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_loop_run.py',
   'PYMODULE'),
  ('gevent.tests.test__core_stat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_stat.py',
   'PYMODULE'),
  ('gevent.tests.test__core_timer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_timer.py',
   'PYMODULE'),
  ('gevent.tests.test__core_watcher',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__core_watcher.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__destroy.py',
   'PYMODULE'),
  ('gevent.tests.test__destroy_default_loop',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__destroy_default_loop.py',
   'PYMODULE'),
  ('gevent.tests.test__doctests',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__doctests.py',
   'PYMODULE'),
  ('gevent.tests.test__environ',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__environ.py',
   'PYMODULE'),
  ('gevent.tests.test__event',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__event.py',
   'PYMODULE'),
  ('gevent.tests.test__events',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__events.py',
   'PYMODULE'),
  ('gevent.tests.test__example_echoserver',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_echoserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_portforwarder',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_portforwarder.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_client',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_udp_client.py',
   'PYMODULE'),
  ('gevent.tests.test__example_udp_server',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_udp_server.py',
   'PYMODULE'),
  ('gevent.tests.test__example_webproxy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_webproxy.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_wsgiserver.py',
   'PYMODULE'),
  ('gevent.tests.test__example_wsgiserver_ssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__example_wsgiserver_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__examples',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__examples.py',
   'PYMODULE'),
  ('gevent.tests.test__exc_info',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__exc_info.py',
   'PYMODULE'),
  ('gevent.tests.test__execmodules',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__execmodules.py',
   'PYMODULE'),
  ('gevent.tests.test__fileobject',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__fileobject.py',
   'PYMODULE'),
  ('gevent.tests.test__getaddrinfo_import',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__getaddrinfo_import.py',
   'PYMODULE'),
  ('gevent.tests.test__greenio',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__greenio.py',
   'PYMODULE'),
  ('gevent.tests.test__greenlet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__greenletset',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__greenletset.py',
   'PYMODULE'),
  ('gevent.tests.test__greenness',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__greenness.py',
   'PYMODULE'),
  ('gevent.tests.test__hub',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__hub.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__hub_join.py',
   'PYMODULE'),
  ('gevent.tests.test__hub_join_timeout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__hub_join_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__import_blocking_in_greenlet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__import_blocking_in_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__import_wait',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__import_wait.py',
   'PYMODULE'),
  ('gevent.tests.test__issue112',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue112.py',
   'PYMODULE'),
  ('gevent.tests.test__issue1686',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue1686.py',
   'PYMODULE'),
  ('gevent.tests.test__issue230',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue230.py',
   'PYMODULE'),
  ('gevent.tests.test__issue330',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue330.py',
   'PYMODULE'),
  ('gevent.tests.test__issue467',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue467.py',
   'PYMODULE'),
  ('gevent.tests.test__issue6',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue6.py',
   'PYMODULE'),
  ('gevent.tests.test__issue600',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue600.py',
   'PYMODULE'),
  ('gevent.tests.test__issue607',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue607.py',
   'PYMODULE'),
  ('gevent.tests.test__issue639',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue639.py',
   'PYMODULE'),
  ('gevent.tests.test__issue_728',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issue_728.py',
   'PYMODULE'),
  ('gevent.tests.test__issues461_471',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__issues461_471.py',
   'PYMODULE'),
  ('gevent.tests.test__iwait',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__iwait.py',
   'PYMODULE'),
  ('gevent.tests.test__joinall',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__joinall.py',
   'PYMODULE'),
  ('gevent.tests.test__local',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__local.py',
   'PYMODULE'),
  ('gevent.tests.test__lock',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__lock.py',
   'PYMODULE'),
  ('gevent.tests.test__loop_callback',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__loop_callback.py',
   'PYMODULE'),
  ('gevent.tests.test__makefile_ref',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__makefile_ref.py',
   'PYMODULE'),
  ('gevent.tests.test__memleak',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__memleak.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_builtins_future',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_builtins_future.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_futures_thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_futures_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_hub_in_thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_hub_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_logging',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_logging.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_module_run',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_module_run.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_multiple_imports',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_multiple_imports.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_queue',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_queue.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_select',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_select.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_selectors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_sigchld.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_2',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_sigchld_3',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_sigchld_3.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning2',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning2.py',
   'PYMODULE'),
  ('gevent.tests.test__monkey_ssl_warning3',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__monkey_ssl_warning3.py',
   'PYMODULE'),
  ('gevent.tests.test__nondefaultloop',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__nondefaultloop.py',
   'PYMODULE'),
  ('gevent.tests.test__order',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__order.py',
   'PYMODULE'),
  ('gevent.tests.test__os',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__os.py',
   'PYMODULE'),
  ('gevent.tests.test__pool',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__pool.py',
   'PYMODULE'),
  ('gevent.tests.test__pywsgi',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__queue',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__queue.py',
   'PYMODULE'),
  ('gevent.tests.test__real_greenlet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__real_greenlet.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__refcount.py',
   'PYMODULE'),
  ('gevent.tests.test__refcount_core',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__refcount_core.py',
   'PYMODULE'),
  ('gevent.tests.test__resolver_dnspython',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__resolver_dnspython.py',
   'PYMODULE'),
  ('gevent.tests.test__select',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__select.py',
   'PYMODULE'),
  ('gevent.tests.test__selectors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__selectors.py',
   'PYMODULE'),
  ('gevent.tests.test__semaphore',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__semaphore.py',
   'PYMODULE'),
  ('gevent.tests.test__server',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__server.py',
   'PYMODULE'),
  ('gevent.tests.test__server_pywsgi',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__server_pywsgi.py',
   'PYMODULE'),
  ('gevent.tests.test__signal',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__signal.py',
   'PYMODULE'),
  ('gevent.tests.test__sleep0',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__sleep0.py',
   'PYMODULE'),
  ('gevent.tests.test__socket',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_close',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_close.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_dns.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_dns6',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_dns6.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_errors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_errors.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ex',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_ex.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_send_memoryview',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_send_memoryview.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_ssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__socket_timeout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socket_timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__socketpair',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__socketpair.py',
   'PYMODULE'),
  ('gevent.tests.test__ssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__ssl.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__subprocess.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_interrupted',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__subprocess_interrupted.py',
   'PYMODULE'),
  ('gevent.tests.test__subprocess_poll',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__subprocess_poll.py',
   'PYMODULE'),
  ('gevent.tests.test__systemerror',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__systemerror.py',
   'PYMODULE'),
  ('gevent.tests.test__thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_2',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_2.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_before_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_holding_lock_while_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_holding_lock_while_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_monkey_in_thread',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_monkey_in_thread.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_native_before_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_native_before_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_no_monkey',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_no_monkey.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_patched_local',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_patched_local.py',
   'PYMODULE'),
  ('gevent.tests.test__threading_vs_settrace',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threading_vs_settrace.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threadpool.py',
   'PYMODULE'),
  ('gevent.tests.test__threadpool_executor_patched',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__threadpool_executor_patched.py',
   'PYMODULE'),
  ('gevent.tests.test__timeout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__timeout.py',
   'PYMODULE'),
  ('gevent.tests.test__util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\gevent\\tests\\test__util.py',
   'PYMODULE'),
  ('gevent.thread',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\thread.py',
   'PYMODULE'),
  ('gevent.threading',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\threading.py',
   'PYMODULE'),
  ('gevent.threadpool',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\threadpool.py',
   'PYMODULE'),
  ('gevent.time',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\time.py',
   'PYMODULE'),
  ('gevent.timeout',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\timeout.py',
   'PYMODULE'),
  ('gevent.util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\util.py',
   'PYMODULE'),
  ('gevent.win32util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\gevent\\win32util.py',
   'PYMODULE'),
  ('glob', 'D:\\Program Files\\Python37\\lib\\glob.py', 'PYMODULE'),
  ('greenlet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\greenlet\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\Program Files\\Python37\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\Python37\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\Python37\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Program Files\\Python37\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\Python37\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\Program Files\\Python37\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\Python37\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\Python37\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\Python37\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\Program Files\\Python37\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\Python37\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'D:\\Program Files\\Python37\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imp', 'D:\\Program Files\\Python37\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\Python37\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\Python37\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\Python37\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\Python37\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\Python37\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\Python37\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\Python37\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._py39compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files\\Python37\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\Python37\\lib\\ipaddress.py', 'PYMODULE'),
  ('jinja2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\Program Files\\Python37\\lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('json', 'D:\\Program Files\\Python37\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\Python37\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\Python37\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\Python37\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files\\Python37\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files\\Python37\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lxml',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'D:\\Program Files\\Python37\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files\\Python37\\lib\\lzma.py', 'PYMODULE'),
  ('markupsafe',
   'D:\\Program Files\\Python37\\lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Program Files\\Python37\\lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\Python37\\lib\\mimetypes.py', 'PYMODULE'),
  ('mock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\mock\\__init__.py',
   'PYMODULE'),
  ('mock.backports',
   'D:\\Program Files\\Python37\\lib\\site-packages\\mock\\backports.py',
   'PYMODULE'),
  ('mock.mock',
   'D:\\Program Files\\Python37\\lib\\site-packages\\mock\\mock.py',
   'PYMODULE'),
  ('mouseinfo',
   'D:\\Program Files\\Python37\\lib\\site-packages\\mouseinfo\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.semaphore_tracker',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\semaphore_tracker.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files\\Python37\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'D:\\Program Files\\Python37\\lib\\netrc.py', 'PYMODULE'),
  ('nntplib', 'D:\\Program Files\\Python37\\lib\\nntplib.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Program Files\\Python37\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Program Files\\Python37\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files\\Python37\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Program Files\\Python37\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'D:\\Program Files\\Python37\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pdb', 'D:\\Program Files\\Python37\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Program Files\\Python37\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\Python37\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Program Files\\Python37\\lib\\platform.py', 'PYMODULE'),
  ('platformdirs',
   'D:\\Program Files\\Python37\\lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\Program Files\\Python37\\lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\Program Files\\Python37\\lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Program Files\\Python37\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\Python37\\lib\\pprint.py', 'PYMODULE'),
  ('proxy_tools',
   'D:\\Program Files\\Python37\\lib\\site-packages\\proxy_tools\\__init__.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psycopg2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\__init__.py',
   'PYMODULE'),
  ('psycopg2._ipaddress',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\_ipaddress.py',
   'PYMODULE'),
  ('psycopg2._json',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\_json.py',
   'PYMODULE'),
  ('psycopg2._range',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\_range.py',
   'PYMODULE'),
  ('psycopg2.extensions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\extensions.py',
   'PYMODULE'),
  ('psycopg2.extras',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\extras.py',
   'PYMODULE'),
  ('psycopg2.sql',
   'D:\\Program Files\\Python37\\lib\\site-packages\\psycopg2\\sql.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Program Files\\Python37\\lib\\py_compile.py', 'PYMODULE'),
  ('pyautogui',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyautogui\\__init__.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_osx',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyautogui\\_pyautogui_osx.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_win',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyautogui\\_pyautogui_win.py',
   'PYMODULE'),
  ('pyautogui._pyautogui_x11',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyautogui\\_pyautogui_x11.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\Python37\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\Python37\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\Python37\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pygetwindow',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pygetwindow\\__init__.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_macos',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pygetwindow\\_pygetwindow_macos.py',
   'PYMODULE'),
  ('pygetwindow._pygetwindow_win',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pygetwindow\\_pygetwindow_win.py',
   'PYMODULE'),
  ('pymsgbox',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pymsgbox\\__init__.py',
   'PYMODULE'),
  ('pymsgbox._native_win',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pymsgbox\\_native_win.py',
   'PYMODULE'),
  ('pyperclip',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pyreadline',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline.clipboard.ironpython_clipboard',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.no_clipboard',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline.clipboard.win32_clipboard',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline.console',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline.console.ansi',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline.console.console',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\console.py',
   'PYMODULE'),
  ('pyreadline.console.event',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\event.py',
   'PYMODULE'),
  ('pyreadline.console.ironpython_console',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline.error',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\error.py',
   'PYMODULE'),
  ('pyreadline.keysyms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline.keysyms.common',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline.keysyms.ironpython_keysyms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.keysyms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline.keysyms.winconstants',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline.lineeditor',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.history',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.lineobj',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline.lineeditor.wordmatcher',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline.logger',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\logger.py',
   'PYMODULE'),
  ('pyreadline.modes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline.modes.basemode',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline.modes.emacs',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline.modes.notemacs',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline.modes.vi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline.py3k_compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline.release',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\release.py',
   'PYMODULE'),
  ('pyreadline.rlmain',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyreadline\\rlmain.py',
   'PYMODULE'),
  ('pyreadline.unicode_helper',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\pyreadline\\unicode_helper.py',
   'PYMODULE'),
  ('pyrect',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyrect\\__init__.py',
   'PYMODULE'),
  ('pyscreeze',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pyscreeze\\__init__.py',
   'PYMODULE'),
  ('pythonnet',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pythonnet\\__init__.py',
   'PYMODULE'),
  ('pytweening',
   'D:\\Program Files\\Python37\\lib\\site-packages\\pytweening\\__init__.py',
   'PYMODULE'),
  ('qtpy',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\__init__.py',
   'PYMODULE'),
  ('qtpy.QtCore',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\QtCore.py',
   'PYMODULE'),
  ('qtpy.QtDataVisualization',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\qtpy\\QtDataVisualization.py',
   'PYMODULE'),
  ('qtpy.QtGui',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\QtGui.py',
   'PYMODULE'),
  ('qtpy.QtNetwork',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\QtNetwork.py',
   'PYMODULE'),
  ('qtpy.QtWebChannel',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\QtWebChannel.py',
   'PYMODULE'),
  ('qtpy.QtWebEngineWidgets',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\qtpy\\QtWebEngineWidgets.py',
   'PYMODULE'),
  ('qtpy.QtWidgets',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\QtWidgets.py',
   'PYMODULE'),
  ('qtpy._patch',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\_patch\\__init__.py',
   'PYMODULE'),
  ('qtpy._patch.qcombobox',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\qtpy\\_patch\\qcombobox.py',
   'PYMODULE'),
  ('qtpy._patch.qheaderview',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\qtpy\\_patch\\qheaderview.py',
   'PYMODULE'),
  ('qtpy._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\_version.py',
   'PYMODULE'),
  ('qtpy.py3compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\qtpy\\py3compat.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files\\Python37\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files\\Python37\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Program Files\\Python37\\lib\\random.py', 'PYMODULE'),
  ('readline',
   'D:\\Program Files\\Python37\\lib\\site-packages\\readline.py',
   'PYMODULE'),
  ('requests',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests_file',
   'D:\\Program Files\\Python37\\lib\\site-packages\\requests_file.py',
   'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files\\Python37\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files\\Python37\\lib\\runpy.py', 'PYMODULE'),
  ('sched', 'D:\\Program Files\\Python37\\lib\\sched.py', 'PYMODULE'),
  ('selectors', 'D:\\Program Files\\Python37\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.py37compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\py37compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Program Files\\Python37\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shiboken2',
   'D:\\Program Files\\Python37\\lib\\site-packages\\shiboken2\\__init__.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files\\Python37\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files\\Python37\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\Python37\\lib\\signal.py', 'PYMODULE'),
  ('simplejson',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\__init__.py',
   'PYMODULE'),
  ('simplejson.compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\compat.py',
   'PYMODULE'),
  ('simplejson.decoder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\decoder.py',
   'PYMODULE'),
  ('simplejson.encoder',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\encoder.py',
   'PYMODULE'),
  ('simplejson.errors',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\errors.py',
   'PYMODULE'),
  ('simplejson.ordered_dict',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\simplejson\\ordered_dict.py',
   'PYMODULE'),
  ('simplejson.raw_json',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\raw_json.py',
   'PYMODULE'),
  ('simplejson.scanner',
   'D:\\Program Files\\Python37\\lib\\site-packages\\simplejson\\scanner.py',
   'PYMODULE'),
  ('site', 'D:\\Program Files\\Python37\\lib\\site.py', 'PYMODULE'),
  ('six',
   'D:\\Program Files\\Python37\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('smtplib', 'D:\\Program Files\\Python37\\lib\\smtplib.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files\\Python37\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\Python37\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'D:\\Program Files\\Python37\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'D:\\Program Files\\Python37\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files\\Python37\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'D:\\Program Files\\Python37\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Program Files\\Python37\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Program Files\\Python37\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Program Files\\Python37\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\Python37\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\Python37\\lib\\tempfile.py', 'PYMODULE'),
  ('test', 'D:\\Program Files\\Python37\\lib\\test\\__init__.py', 'PYMODULE'),
  ('test.libregrtest',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\__init__.py',
   'PYMODULE'),
  ('test.libregrtest.cmdline',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\cmdline.py',
   'PYMODULE'),
  ('test.libregrtest.main',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\main.py',
   'PYMODULE'),
  ('test.libregrtest.refleak',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\refleak.py',
   'PYMODULE'),
  ('test.libregrtest.runtest',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\runtest.py',
   'PYMODULE'),
  ('test.libregrtest.runtest_mp',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\runtest_mp.py',
   'PYMODULE'),
  ('test.libregrtest.save_env',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\save_env.py',
   'PYMODULE'),
  ('test.libregrtest.setup',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\setup.py',
   'PYMODULE'),
  ('test.libregrtest.utils',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\utils.py',
   'PYMODULE'),
  ('test.libregrtest.win_utils',
   'D:\\Program Files\\Python37\\lib\\test\\libregrtest\\win_utils.py',
   'PYMODULE'),
  ('test.support',
   'D:\\Program Files\\Python37\\lib\\test\\support\\__init__.py',
   'PYMODULE'),
  ('test.support.script_helper',
   'D:\\Program Files\\Python37\\lib\\test\\support\\script_helper.py',
   'PYMODULE'),
  ('test.support.testresult',
   'D:\\Program Files\\Python37\\lib\\test\\support\\testresult.py',
   'PYMODULE'),
  ('test.test_support',
   'D:\\Program Files\\Python37\\lib\\test\\test_support.py',
   'PYMODULE'),
  ('tests',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tests\\__init__.py',
   'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\Python37\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\Python37\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\Program Files\\Python37\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Program Files\\Python37\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files\\Python37\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Program Files\\Python37\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Program Files\\Python37\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Program Files\\Python37\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tldextract',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tldextract\\__init__.py',
   'PYMODULE'),
  ('tldextract._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tldextract\\_version.py',
   'PYMODULE'),
  ('tldextract.cache',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tldextract\\cache.py',
   'PYMODULE'),
  ('tldextract.remote',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tldextract\\remote.py',
   'PYMODULE'),
  ('tldextract.suffix_list',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\tldextract\\suffix_list.py',
   'PYMODULE'),
  ('tldextract.tldextract',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tldextract\\tldextract.py',
   'PYMODULE'),
  ('tornado',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\__init__.py',
   'PYMODULE'),
  ('tornado.concurrent',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\concurrent.py',
   'PYMODULE'),
  ('tornado.escape',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\escape.py',
   'PYMODULE'),
  ('tornado.gen',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\gen.py',
   'PYMODULE'),
  ('tornado.http1connection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\tornado\\http1connection.py',
   'PYMODULE'),
  ('tornado.httpserver',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\httpserver.py',
   'PYMODULE'),
  ('tornado.httputil',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\httputil.py',
   'PYMODULE'),
  ('tornado.ioloop',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\ioloop.py',
   'PYMODULE'),
  ('tornado.iostream',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\iostream.py',
   'PYMODULE'),
  ('tornado.log',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\log.py',
   'PYMODULE'),
  ('tornado.netutil',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\netutil.py',
   'PYMODULE'),
  ('tornado.options',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\options.py',
   'PYMODULE'),
  ('tornado.platform',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\tornado\\platform\\__init__.py',
   'PYMODULE'),
  ('tornado.platform.asyncio',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\tornado\\platform\\asyncio.py',
   'PYMODULE'),
  ('tornado.process',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\process.py',
   'PYMODULE'),
  ('tornado.tcpserver',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\tcpserver.py',
   'PYMODULE'),
  ('tornado.util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\util.py',
   'PYMODULE'),
  ('tornado.wsgi',
   'D:\\Program Files\\Python37\\lib\\site-packages\\tornado\\wsgi.py',
   'PYMODULE'),
  ('trace', 'D:\\Program Files\\Python37\\lib\\trace.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\Python37\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\Python37\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Program Files\\Python37\\lib\\typing.py', 'PYMODULE'),
  ('typing_extensions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Program Files\\Python37\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files\\Python37\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files\\Python37\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files\\Python37\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Program Files\\Python37\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files\\Python37\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files\\Python37\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files\\Python37\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files\\Python37\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files\\Python37\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib3',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._collections',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._version',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib._appengine_environ',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\_appengine_environ.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.packages',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\backports\\__init__.py',
   'PYMODULE'),
  ('urllib3.packages.backports.makefile',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\packages\\backports\\makefile.py',
   'PYMODULE'),
  ('urllib3.packages.six',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\packages\\six.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.request',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\request.py',
   'PYMODULE'),
  ('urllib3.response',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.queue',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\queue.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'D:\\Program Files\\Python37\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu', 'D:\\Program Files\\Python37\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'D:\\Program Files\\Python37\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'D:\\Program Files\\Python37\\lib\\webbrowser.py', 'PYMODULE'),
  ('websocket',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'D:\\Program Files\\Python37\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('webview',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\__init__.py',
   'PYMODULE'),
  ('webview.event',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\event.py',
   'PYMODULE'),
  ('webview.guilib',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\guilib.py',
   'PYMODULE'),
  ('webview.http',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\http.py',
   'PYMODULE'),
  ('webview.js',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\__init__.py',
   'PYMODULE'),
  ('webview.js.alert',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\alert.py',
   'PYMODULE'),
  ('webview.js.api',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\api.py',
   'PYMODULE'),
  ('webview.js.css',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\css.py',
   'PYMODULE'),
  ('webview.js.dom',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\dom.py',
   'PYMODULE'),
  ('webview.js.event',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\event.py',
   'PYMODULE'),
  ('webview.js.mouse',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\mouse.py',
   'PYMODULE'),
  ('webview.js.npo',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\js\\npo.py',
   'PYMODULE'),
  ('webview.localization',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\localization.py',
   'PYMODULE'),
  ('webview.menu',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\menu.py',
   'PYMODULE'),
  ('webview.platforms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\__init__.py',
   'PYMODULE'),
  ('webview.platforms.cef',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\cef.py',
   'PYMODULE'),
  ('webview.platforms.cocoa',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\cocoa.py',
   'PYMODULE'),
  ('webview.platforms.edgechromium',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\edgechromium.py',
   'PYMODULE'),
  ('webview.platforms.gtk',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\gtk.py',
   'PYMODULE'),
  ('webview.platforms.mshtml',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\mshtml.py',
   'PYMODULE'),
  ('webview.platforms.qt',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\platforms\\qt.py',
   'PYMODULE'),
  ('webview.platforms.winforms',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\webview\\platforms\\winforms.py',
   'PYMODULE'),
  ('webview.screen',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\screen.py',
   'PYMODULE'),
  ('webview.util',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\util.py',
   'PYMODULE'),
  ('webview.window',
   'D:\\Program Files\\Python37\\lib\\site-packages\\webview\\window.py',
   'PYMODULE'),
  ('win32con',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'D:\\Program Files\\Python37\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wsgiref',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\__init__.py',
   'PYMODULE'),
  ('wsgiref.handlers',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\handlers.py',
   'PYMODULE'),
  ('wsgiref.headers',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\headers.py',
   'PYMODULE'),
  ('wsgiref.simple_server',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\simple_server.py',
   'PYMODULE'),
  ('wsgiref.util',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\util.py',
   'PYMODULE'),
  ('wsgiref.validate',
   'D:\\Program Files\\Python37\\lib\\wsgiref\\validate.py',
   'PYMODULE'),
  ('xml', 'D:\\Program Files\\Python37\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Program Files\\Python37\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\Python37\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\Python37\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\Python37\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files\\Python37\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files\\Python37\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'D:\\Program Files\\Python37\\lib\\zipfile.py', 'PYMODULE'),
  ('zipp',
   'D:\\Program Files\\Python37\\lib\\site-packages\\zipp.py',
   'PYMODULE'),
  ('zope', '-', 'PYMODULE'),
  ('zope.event',
   'D:\\Program Files\\Python37\\lib\\site-packages\\zope\\event\\__init__.py',
   'PYMODULE'),
  ('zope.interface',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\__init__.py',
   'PYMODULE'),
  ('zope.interface._compat',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\_compat.py',
   'PYMODULE'),
  ('zope.interface.advice',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\advice.py',
   'PYMODULE'),
  ('zope.interface.declarations',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\declarations.py',
   'PYMODULE'),
  ('zope.interface.exceptions',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\exceptions.py',
   'PYMODULE'),
  ('zope.interface.interface',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\interface.py',
   'PYMODULE'),
  ('zope.interface.interfaces',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\interfaces.py',
   'PYMODULE'),
  ('zope.interface.ro',
   'D:\\Program Files\\Python37\\lib\\site-packages\\zope\\interface\\ro.py',
   'PYMODULE'),
  ('zope.interface.verify',
   'D:\\Program '
   'Files\\Python37\\lib\\site-packages\\zope\\interface\\verify.py',
   'PYMODULE')])
